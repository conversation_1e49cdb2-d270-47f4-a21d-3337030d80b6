{"extends": "./tsconfig.json", "compilerOptions": {"target": "ES2020", "lib": ["DOM", "DOM.Iterable", "ES6"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx"}, "include": ["src/renderer/**/*", "src/shared/**/*"], "exclude": ["node_modules", "dist", "src/main/**/*", "src/preload/**/*"]}