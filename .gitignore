# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/
out/
app/
release/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Electron specific
# Don't ignore the main database schema
!src/main/database/schema.sql

# Database files (user data)
*.db
*.sqlite
*.sqlite3

# Temporary files
*.tmp
*.temp

# Windows specific
desktop.ini

# macOS specific
.AppleDouble
.LSOverride

# Linux specific
*~

# Backup files
*.bak
*.backup

# Test coverage
coverage/
.nyc_output/

# TypeScript cache
*.tsbuildinfo

# ESLint cache
.eslintcache

# Prettier cache
.prettiercache

# Husky
.husky/_
