import * as crypto from 'crypto';
import { User } from '../../shared/types/database';

/**
 * Session management service for handling user authentication sessions
 */
export class SessionService {
  private activeSessions: Map<string, {
    userId: number;
    user: Omit<User, 'passwordHash' | 'salt'>;
    createdAt: Date;
    lastAccessedAt: Date;
    expiresAt: Date;
  }> = new Map();

  private readonly SESSION_DURATION = 8 * 60 * 60 * 1000; // 8 hours in milliseconds
  private readonly CLEANUP_INTERVAL = 60 * 60 * 1000; // 1 hour in milliseconds
  private cleanupTimer: NodeJS.Timeout | undefined;

  constructor() {
    this.startCleanupTimer();
  }

  /**
   * Create a new session for a user
   */
  createSession(user: Omit<User, 'passwordHash' | 'salt'>): string {
    const sessionId = this.generateSessionId();
    const now = new Date();
    const expiresAt = new Date(now.getTime() + this.SESSION_DURATION);

    this.activeSessions.set(sessionId, {
      userId: user.id,
      user,
      createdAt: now,
      lastAccessedAt: now,
      expiresAt,
    });

    console.log(`✅ Session created for user: ${user.username} (${sessionId})`);
    return sessionId;
  }

  /**
   * Validate and refresh a session
   */
  validateSession(sessionId: string): { isValid: boolean; user?: Omit<User, 'passwordHash' | 'salt'> } {
    const session = this.activeSessions.get(sessionId);
    
    if (!session) {
      return { isValid: false };
    }

    const now = new Date();
    
    // Check if session has expired
    if (now > session.expiresAt) {
      this.activeSessions.delete(sessionId);
      console.log(`⏰ Session expired for user: ${session.user.username}`);
      return { isValid: false };
    }

    // Check if user is still active
    if (!session.user.isActive) {
      this.activeSessions.delete(sessionId);
      console.log(`🚫 Session invalidated - user inactive: ${session.user.username}`);
      return { isValid: false };
    }

    // Update last accessed time and extend expiration
    session.lastAccessedAt = now;
    session.expiresAt = new Date(now.getTime() + this.SESSION_DURATION);

    return { isValid: true, user: session.user };
  }

  /**
   * Get current user from session
   */
  getCurrentUser(sessionId: string): Omit<User, 'passwordHash' | 'salt'> | null {
    const validation = this.validateSession(sessionId);
    return validation.isValid ? validation.user! : null;
  }

  /**
   * Destroy a specific session
   */
  destroySession(sessionId: string): boolean {
    const session = this.activeSessions.get(sessionId);
    if (session) {
      this.activeSessions.delete(sessionId);
      console.log(`🗑️ Session destroyed for user: ${session.user.username}`);
      return true;
    }
    return false;
  }

  /**
   * Destroy all sessions for a specific user
   */
  destroyUserSessions(userId: number): number {
    let destroyedCount = 0;
    
    for (const [sessionId, session] of this.activeSessions.entries()) {
      if (session.userId === userId) {
        this.activeSessions.delete(sessionId);
        destroyedCount++;
      }
    }

    if (destroyedCount > 0) {
      console.log(`🗑️ Destroyed ${destroyedCount} sessions for user ID: ${userId}`);
    }

    return destroyedCount;
  }

  /**
   * Get all active sessions (for admin purposes)
   */
  getActiveSessions(): Array<{
    sessionId: string;
    userId: number;
    username: string;
    role: string;
    createdAt: Date;
    lastAccessedAt: Date;
    expiresAt: Date;
  }> {
    const sessions = [];
    
    for (const [sessionId, session] of this.activeSessions.entries()) {
      sessions.push({
        sessionId,
        userId: session.userId,
        username: session.user.username,
        role: session.user.role,
        createdAt: session.createdAt,
        lastAccessedAt: session.lastAccessedAt,
        expiresAt: session.expiresAt,
      });
    }

    return sessions.sort((a, b) => b.lastAccessedAt.getTime() - a.lastAccessedAt.getTime());
  }

  /**
   * Get session statistics
   */
  getSessionStats(): {
    totalActiveSessions: number;
    sessionsByRole: Record<string, number>;
    oldestSession?: Date;
    newestSession?: Date;
  } {
    const sessions = Array.from(this.activeSessions.values());
    const sessionsByRole: Record<string, number> = {};

    sessions.forEach(session => {
      sessionsByRole[session.user.role] = (sessionsByRole[session.user.role] || 0) + 1;
    });

    const createdTimes = sessions.map(s => s.createdAt);

    const result: {
      totalActiveSessions: number;
      sessionsByRole: Record<string, number>;
      oldestSession?: Date;
      newestSession?: Date;
    } = {
      totalActiveSessions: sessions.length,
      sessionsByRole,
    };

    if (createdTimes.length > 0) {
      result.oldestSession = new Date(Math.min(...createdTimes.map(d => d.getTime())));
      result.newestSession = new Date(Math.max(...createdTimes.map(d => d.getTime())));
    }

    return result;
  }

  /**
   * Generate a cryptographically secure session ID
   */
  private generateSessionId(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Start the cleanup timer to remove expired sessions
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanupExpiredSessions();
    }, this.CLEANUP_INTERVAL);
  }

  /**
   * Clean up expired sessions
   */
  private cleanupExpiredSessions(): void {
    const now = new Date();
    let cleanedCount = 0;

    for (const [sessionId, session] of this.activeSessions.entries()) {
      if (now > session.expiresAt) {
        this.activeSessions.delete(sessionId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`🧹 Cleaned up ${cleanedCount} expired sessions`);
    }
  }

  /**
   * Stop the cleanup timer (for shutdown)
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }
    this.activeSessions.clear();
    console.log('🛑 Session service destroyed');
  }
}

// Export singleton instance
export const sessionService = new SessionService();
