import { databaseService } from '../database/database';
import { Product, QueryResult, PaginatedResult, SearchFilters, SortOptions } from '../../shared/types/database';

/**
 * Product service for managing handcraft products
 */
export class ProductService {

  /**
   * Create a new product
   */
  async createProduct(productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Promise<QueryResult<Product>> {
    try {
      const result = databaseService.execute(`
        INSERT INTO products (
          name, name_hindi, description, description_hindi, category_id, artist_id,
          sku, barcode, cost_price, selling_price, mrp, weight, dimensions,
          materials, materials_hindi, colors, is_active, tags, images
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        productData.name,
        productData.nameHindi,
        productData.description || null,
        productData.descriptionHindi || null,
        productData.categoryId,
        productData.artistId,
        productData.sku,
        productData.barcode || null,
        productData.costPrice,
        productData.sellingPrice,
        productData.mrp,
        productData.weight || null,
        productData.dimensions || null,
        productData.materials || null,
        productData.materialsHindi || null,
        productData.colors || null,
        productData.isActive ? 1 : 0,
        productData.tags || null,
        productData.images || null
      ]);

      if (!result.success || !result.data) {
        return { success: false, error: result.error || 'Unknown error' };
      }

      // Get the created product
      const createdProduct = await this.getProductById(result.data.lastInsertRowid as number);
      return createdProduct;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create product'
      };
    }
  }

  /**
   * Get product by ID with related data
   */
  async getProductById(id: number): Promise<QueryResult<Product & { categoryName?: string; artistName?: string }>> {
    return databaseService.queryOne<Product & { categoryName?: string; artistName?: string }>(`
      SELECT 
        p.*,
        c.name as categoryName,
        a.name as artistName
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN artists a ON p.artist_id = a.id
      WHERE p.id = ?
    `, [id]);
  }

  /**
   * Get product by SKU
   */
  async getProductBySku(sku: string): Promise<QueryResult<Product>> {
    return databaseService.queryOne<Product>(`
      SELECT * FROM products WHERE sku = ?
    `, [sku]);
  }

  /**
   * Get single product by ID (alias for getProductById)
   */
  async getProduct(id: number): Promise<QueryResult<Product & { categoryName?: string; artistName?: string }>> {
    return this.getProductById(id);
  }

  /**
   * Get all products with pagination and filtering
   */
  async getProducts(params?: { page?: number; limit?: number; search?: string; filters?: SearchFilters; sort?: SortOptions }): Promise<QueryResult<PaginatedResult<Product & { categoryName?: string; artistName?: string }>>> {
    const { page = 1, limit = 50, search = '', filters, sort } = params || {};
    let sql = `
      SELECT
        p.*,
        c.name as categoryName,
        a.name as artistName,
        i.current_stock,
        i.available_stock
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN artists a ON p.artist_id = a.id
      LEFT JOIN inventory i ON p.id = i.product_id
    `;

    let queryParams: any[] = [];
    const conditions: string[] = ['p.is_active = 1'];

    // Handle search parameter
    if (search) {
      conditions.push('(p.name LIKE ? OR p.name_hindi LIKE ? OR p.description LIKE ? OR p.sku LIKE ?)');
      const searchTerm = `%${search}%`;
      queryParams.push(searchTerm, searchTerm, searchTerm, searchTerm);
    }

    // Handle additional filters
    if (filters) {
      const { where, params: whereParams } = databaseService.buildWhereClause(filters);
      if (where) {
        const filterConditions = where.replace('WHERE ', '').replace(/\b(name|description|created_at|selling_price|is_active)\b/g, 'p.$1');
        conditions.push(filterConditions);
        queryParams.push(...whereParams);
      }
    }

    if (conditions.length > 0) {
      sql += ` WHERE ${conditions.join(' AND ')}`;
    }

    sql += ` ${databaseService.buildOrderClause(sort)}`;

    return databaseService.paginate<Product & { categoryName?: string; artistName?: string }>(sql, queryParams, page, limit);
  }

  /**
   * Update product
   */
  async updateProduct(id: number, productData: Partial<Omit<Product, 'id' | 'createdAt' | 'updatedAt'>>): Promise<QueryResult<Product>> {
    try {
      const updates: string[] = [];
      const params: any[] = [];

      if (productData.name !== undefined) {
        updates.push('name = ?');
        params.push(productData.name);
      }

      if (productData.nameHindi !== undefined) {
        updates.push('name_hindi = ?');
        params.push(productData.nameHindi);
      }

      if (productData.description !== undefined) {
        updates.push('description = ?');
        params.push(productData.description);
      }

      if (productData.descriptionHindi !== undefined) {
        updates.push('description_hindi = ?');
        params.push(productData.descriptionHindi);
      }

      if (productData.categoryId !== undefined) {
        updates.push('category_id = ?');
        params.push(productData.categoryId);
      }

      if (productData.artistId !== undefined) {
        updates.push('artist_id = ?');
        params.push(productData.artistId);
      }

      if (productData.sku !== undefined) {
        updates.push('sku = ?');
        params.push(productData.sku);
      }

      if (productData.barcode !== undefined) {
        updates.push('barcode = ?');
        params.push(productData.barcode);
      }

      if (productData.costPrice !== undefined) {
        updates.push('cost_price = ?');
        params.push(productData.costPrice);
      }

      if (productData.sellingPrice !== undefined) {
        updates.push('selling_price = ?');
        params.push(productData.sellingPrice);
      }

      if (productData.mrp !== undefined) {
        updates.push('mrp = ?');
        params.push(productData.mrp);
      }

      if (productData.weight !== undefined) {
        updates.push('weight = ?');
        params.push(productData.weight);
      }

      if (productData.dimensions !== undefined) {
        updates.push('dimensions = ?');
        params.push(productData.dimensions);
      }

      if (productData.materials !== undefined) {
        updates.push('materials = ?');
        params.push(productData.materials);
      }

      if (productData.materialsHindi !== undefined) {
        updates.push('materials_hindi = ?');
        params.push(productData.materialsHindi);
      }

      if (productData.colors !== undefined) {
        updates.push('colors = ?');
        params.push(productData.colors);
      }

      if (productData.isActive !== undefined) {
        updates.push('is_active = ?');
        params.push(productData.isActive ? 1 : 0);
      }

      if (productData.tags !== undefined) {
        updates.push('tags = ?');
        params.push(productData.tags);
      }

      if (productData.images !== undefined) {
        updates.push('images = ?');
        params.push(productData.images);
      }

      if (updates.length === 0) {
        return { success: false, error: 'No fields to update' };
      }

      updates.push('updated_at = datetime("now")');
      params.push(id);

      const result = databaseService.execute(`
        UPDATE products SET ${updates.join(', ')} WHERE id = ?
      `, params);

      if (!result.success) {
        return { success: false, error: result.error || 'Unknown error' };
      }

      // Get the updated product
      return await this.getProductById(id);
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update product'
      };
    }
  }

  /**
   * Delete product (soft delete by setting inactive)
   */
  async deleteProduct(id: number): Promise<QueryResult<boolean>> {
    const result = databaseService.execute(`
      UPDATE products SET is_active = 0, updated_at = datetime('now') WHERE id = ?
    `, [id]);

    return {
      success: result.success,
      data: result.success,
      error: result.error || undefined
    };
  }

  /**
   * Search products by name or description
   */
  async searchProducts(query: string, limit: number = 20): Promise<QueryResult<Product[]>> {
    return databaseService.query<Product>(`
      SELECT 
        p.*,
        c.name as categoryName,
        a.name as artistName
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN artists a ON p.artist_id = a.id
      WHERE p.is_active = 1 
        AND (p.name LIKE ? OR p.name_hindi LIKE ? OR p.description LIKE ? OR p.sku LIKE ?)
      ORDER BY p.name
      LIMIT ?
    `, [`%${query}%`, `%${query}%`, `%${query}%`, `%${query}%`, limit]);
  }

  /**
   * Get products by category
   */
  async getProductsByCategory(categoryId: number, limit: number = 50): Promise<QueryResult<Product[]>> {
    return databaseService.query<Product>(`
      SELECT 
        p.*,
        c.name as categoryName,
        a.name as artistName
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN artists a ON p.artist_id = a.id
      WHERE p.category_id = ? AND p.is_active = 1
      ORDER BY p.name
      LIMIT ?
    `, [categoryId, limit]);
  }

  /**
   * Get products by artist
   */
  async getProductsByArtist(artistId: number, limit: number = 50): Promise<QueryResult<Product[]>> {
    return databaseService.query<Product>(`
      SELECT 
        p.*,
        c.name as categoryName,
        a.name as artistName
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN artists a ON p.artist_id = a.id
      WHERE p.artist_id = ? AND p.is_active = 1
      ORDER BY p.name
      LIMIT ?
    `, [artistId, limit]);
  }

  /**
   * Get low stock products
   */
  async getLowStockProducts(): Promise<QueryResult<(Product & { currentStock: number; minStockLevel: number })[]>> {
    return databaseService.query<Product & { currentStock: number; minStockLevel: number }>(`
      SELECT 
        p.*,
        i.current_stock as currentStock,
        i.min_stock_level as minStockLevel
      FROM products p
      INNER JOIN inventory i ON p.id = i.product_id
      WHERE p.is_active = 1 AND i.current_stock <= i.min_stock_level
      ORDER BY i.current_stock ASC
    `);
  }

  /**
   * Generate unique SKU
   */
  async generateSku(categoryId: number, artistId: number): Promise<string> {
    const categoryResult = await databaseService.queryOne<{ name: string }>('SELECT name FROM categories WHERE id = ?', [categoryId]);
    const artistResult = await databaseService.queryOne<{ name: string }>('SELECT name FROM artists WHERE id = ?', [artistId]);
    
    const categoryCode = categoryResult.data?.name.substring(0, 3).toUpperCase() || 'CAT';
    const artistCode = artistResult.data?.name.substring(0, 3).toUpperCase() || 'ART';
    const timestamp = Date.now().toString().slice(-6);
    
    return `${categoryCode}-${artistCode}-${timestamp}`;
  }
}

export const productService = new ProductService();
