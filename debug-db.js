const Database = require('better-sqlite3');
const path = require('path');
const { app } = require('electron');

// Get the database path (same as in the main app)
const userDataPath = process.env.APPDATA || (process.platform == 'darwin' ? process.env.HOME + '/Library/Preferences' : process.env.HOME + "/.local/share");
const dbPath = path.join(userDataPath, 'मैथिली विकास कोष Shop Management', 'maithili-shop.db');

console.log('Database path:', dbPath);

try {
  const db = new Database(dbPath);
  
  // Check all users and their status
  console.log('\n=== All Users ===');
  const users = db.prepare('SELECT id, username, email, role, full_name, is_active, created_at, last_login_at FROM users').all();
  console.table(users);
  
  // Check specifically for admin user
  console.log('\n=== Admin User Details ===');
  const admin = db.prepare('SELECT * FROM users WHERE username = ?').get('admin');
  if (admin) {
    console.log('Admin user found:');
    console.log('ID:', admin.id);
    console.log('Username:', admin.username);
    console.log('Email:', admin.email);
    console.log('Role:', admin.role);
    console.log('Full Name:', admin.full_name);
    console.log('Is Active:', admin.is_active, '(type:', typeof admin.is_active, ')');
    console.log('Created At:', admin.created_at);
    console.log('Last Login:', admin.last_login_at);
  } else {
    console.log('Admin user not found!');
  }
  
  // Check the raw value of is_active
  console.log('\n=== Raw is_active values ===');
  const rawValues = db.prepare('SELECT username, is_active, typeof(is_active) as type FROM users').all();
  rawValues.forEach(row => {
    console.log(`${row.username}: ${row.is_active} (${row.type})`);
  });
  
  db.close();
} catch (error) {
  console.error('Error accessing database:', error);
}
