import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { InventoryItem } from '../../../shared/types/database';

interface InventoryState {
  selectedItem: InventoryItem | null;
  lowStockThreshold: number;
  sortBy: 'product_name' | 'current_stock' | 'last_updated';
  sortOrder: 'asc' | 'desc';
  showLowStockOnly: boolean;
  pageSize: number;
  currentPage: number;
}

const initialState: InventoryState = {
  selectedItem: null,
  lowStockThreshold: 10,
  sortBy: 'last_updated',
  sortOrder: 'desc',
  showLowStockOnly: false,
  pageSize: 25,
  currentPage: 1,
};

const inventorySlice = createSlice({
  name: 'inventory',
  initialState,
  reducers: {
    setSelectedItem: (state, action: PayloadAction<InventoryItem | null>) => {
      state.selectedItem = action.payload;
    },
    setLowStockThreshold: (state, action: PayloadAction<number>) => {
      state.lowStockThreshold = action.payload;
    },
    setSortBy: (state, action: PayloadAction<InventoryState['sortBy']>) => {
      state.sortBy = action.payload;
    },
    setSortOrder: (state, action: PayloadAction<InventoryState['sortOrder']>) => {
      state.sortOrder = action.payload;
    },
    setShowLowStockOnly: (state, action: PayloadAction<boolean>) => {
      state.showLowStockOnly = action.payload;
    },
    setPageSize: (state, action: PayloadAction<number>) => {
      state.pageSize = action.payload;
      state.currentPage = 1;
    },
    setCurrentPage: (state, action: PayloadAction<number>) => {
      state.currentPage = action.payload;
    },
  },
});

export const {
  setSelectedItem,
  setLowStockThreshold,
  setSortBy,
  setSortOrder,
  setShowLowStockOnly,
  setPageSize,
  setCurrentPage,
} = inventorySlice.actions;

export default inventorySlice.reducer;
