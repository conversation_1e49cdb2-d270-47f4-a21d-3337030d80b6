import { databaseService } from './database';
import { userService } from '../services/userService';

/**
 * Seed sample data for Maithili Vikas Kosh Shop Management System
 */
export class SeedDataService {

  /**
   * Seed all sample data
   */
  async seedAll(): Promise<void> {
    try {
      console.log('🌱 Starting data seeding...');

      await this.seedUsers();
      await this.seedCategories();
      await this.seedArtists();
      await this.seedProducts();
      await this.seedCustomers();
      await this.seedInventory();

      console.log('✅ Data seeding completed successfully');
    } catch (error) {
      console.error('❌ Data seeding failed:', error);
      throw error;
    }
  }

  /**
   * Seed users
   */
  private async seedUsers(): Promise<void> {
    console.log('👥 Seeding users...');

    // Create default admin if no users exist
    await userService.createDefaultAdmin();

    // Create additional sample users
    const sampleUsers = [
      {
        username: 'staff1',
        email: '<EMAIL>',
        password: 'staff123',
        role: 'staff' as const,
        fullName: '<PERSON><PERSON>',
        phone: '+91-9876543210',
        isActive: true
      },
      {
        username: 'viewer1',
        email: '<EMAIL>',
        password: 'viewer123',
        role: 'viewer' as const,
        fullName: 'Rajesh Kumar',
        phone: '+91-9876543211',
        isActive: true
      }
    ];

    for (const user of sampleUsers) {
      const existingUser = await userService.getUserByUsername(user.username);
      if (!existingUser.success || !existingUser.data) {
        await userService.createUser(user);
      }
    }
  }

  /**
   * Seed categories
   */
  private async seedCategories(): Promise<void> {
    console.log('📂 Seeding categories...');

    const categories = [
      {
        name: 'Paintings',
        nameHindi: 'चित्रकारी',
        description: 'Traditional Mithila paintings and artwork',
        descriptionHindi: 'पारंपरिक मिथिला चित्रकारी और कलाकृति',
        sortOrder: 1
      },
      {
        name: 'Sarees',
        nameHindi: 'साड़ी',
        description: 'Beautiful Mithila handwoven sarees',
        descriptionHindi: 'सुंदर मिथिला हस्तनिर्मित साड़ी',
        sortOrder: 2
      },
      {
        name: 'Bags',
        nameHindi: 'हैंडबैग',
        description: 'Handcrafted bags with Mithila art',
        descriptionHindi: 'मिथिला कला के साथ हस्तनिर्मित बैग',
        sortOrder: 3
      },
      {
        name: 'Home Decor',
        nameHindi: 'घर की सजावट',
        description: 'Decorative items for home',
        descriptionHindi: 'घर के लिए सजावटी सामान',
        sortOrder: 4
      },
      {
        name: 'Jewelry',
        nameHindi: 'आभूषण',
        description: 'Traditional Mithila jewelry',
        descriptionHindi: 'पारंपरिक मिथिला आभूषण',
        sortOrder: 5
      }
    ];

    for (const category of categories) {
      const existing = await databaseService.queryOne(
        'SELECT id FROM categories WHERE name = ?',
        [category.name]
      );

      if (!existing.success || !existing.data) {
        await databaseService.execute(`
          INSERT INTO categories (name, name_hindi, description, description_hindi, sort_order, is_active)
          VALUES (?, ?, ?, ?, ?, 1)
        `, [category.name, category.nameHindi, category.description, category.descriptionHindi, category.sortOrder]);
      }
    }
  }

  /**
   * Seed artists
   */
  private async seedArtists(): Promise<void> {
    console.log('🎨 Seeding artists...');

    const artists = [
      {
        name: 'Sita Devi',
        nameHindi: 'सीता देवी',
        email: '<EMAIL>',
        phone: '+91-9876543220',
        address: 'Madhubani, Bihar',
        addressHindi: 'मधुबनी, बिहार',
        specialization: 'Madhubani Painting',
        specializationHindi: 'मधुबनी चित्रकारी',
        commissionRate: 15.0
      },
      {
        name: 'Ganga Devi',
        nameHindi: 'गंगा देवी',
        email: '<EMAIL>',
        phone: '+91-9876543221',
        address: 'Darbhanga, Bihar',
        addressHindi: 'दरभंगा, बिहार',
        specialization: 'Mithila Saree Weaving',
        specializationHindi: 'मिथिला साड़ी बुनाई',
        commissionRate: 12.0
      },
      {
        name: 'Radha Devi',
        nameHindi: 'राधा देवी',
        email: '<EMAIL>',
        phone: '+91-9876543222',
        address: 'Samastipur, Bihar',
        addressHindi: 'समस्तीपुर, बिहार',
        specialization: 'Handcrafted Bags',
        specializationHindi: 'हस्तनिर्मित बैग',
        commissionRate: 10.0
      },
      {
        name: 'Kamala Devi',
        nameHindi: 'कमला देवी',
        email: '<EMAIL>',
        phone: '+91-9876543223',
        address: 'Sitamarhi, Bihar',
        addressHindi: 'सीतामढ़ी, बिहार',
        specialization: 'Home Decor Items',
        specializationHindi: 'घर की सजावट की वस्तुएं',
        commissionRate: 8.0
      },
      {
        name: 'Sunita Devi',
        nameHindi: 'सुनीता देवी',
        email: '<EMAIL>',
        phone: '+91-9876543224',
        address: 'Muzaffarpur, Bihar',
        addressHindi: 'मुजफ्फरपुर, बिहार',
        specialization: 'Traditional Jewelry',
        specializationHindi: 'पारंपरिक आभूषण',
        commissionRate: 20.0
      }
    ];

    for (const artist of artists) {
      const existing = await databaseService.queryOne(
        'SELECT id FROM artists WHERE name = ?',
        [artist.name]
      );

      if (!existing.success || !existing.data) {
        await databaseService.execute(`
          INSERT INTO artists (
            name, name_hindi, email, phone, address, address_hindi,
            specialization, specialization_hindi, commission_rate, is_active
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
        `, [
          artist.name, artist.nameHindi, artist.email, artist.phone,
          artist.address, artist.addressHindi, artist.specialization,
          artist.specializationHindi, artist.commissionRate
        ]);
      }
    }
  }

  /**
   * Seed products
   */
  private async seedProducts(): Promise<void> {
    console.log('🛍️ Seeding products...');

    const products = [
      {
        name: 'Madhubani Fish Painting',
        nameHindi: 'मधुबनी मछली चित्रकारी',
        description: 'Beautiful traditional fish painting on handmade paper',
        descriptionHindi: 'हस्तनिर्मित कागज पर सुंदर पारंपरिक मछली चित्रकारी',
        categoryName: 'Paintings',
        artistName: 'Sita Devi',
        sku: 'PAI-SIT-001',
        costPrice: 800,
        sellingPrice: 1200,
        mrp: 1500,
        dimensions: '30x40 cm',
        materials: 'Handmade paper, Natural colors',
        materialsHindi: 'हस्तनिर्मित कागज, प्राकृतिक रंग',
        colors: 'Red, Blue, Yellow, Green'
      },
      {
        name: 'Mithila Peacock Saree',
        nameHindi: 'मिथिला मोर साड़ी',
        description: 'Elegant saree with peacock motifs',
        descriptionHindi: 'मोर के डिजाइन के साथ सुंदर साड़ी',
        categoryName: 'Sarees',
        artistName: 'Ganga Devi',
        sku: 'SAR-GAN-001',
        costPrice: 2000,
        sellingPrice: 3500,
        mrp: 4000,
        materials: 'Cotton, Natural dyes',
        materialsHindi: 'कपास, प्राकृतिक रंग',
        colors: 'Maroon, Gold'
      },
      {
        name: 'Handcrafted Mithila Bag',
        nameHindi: 'हस्तनिर्मित मिथिला बैग',
        description: 'Beautiful handcrafted bag with traditional designs',
        descriptionHindi: 'पारंपरिक डिजाइन के साथ सुंदर हस्तनिर्मित बैग',
        categoryName: 'Bags',
        artistName: 'Radha Devi',
        sku: 'BAG-RAD-001',
        costPrice: 600,
        sellingPrice: 1200,
        mrp: 1500,
        dimensions: '35x25x10 cm',
        materials: 'Jute, Cotton thread',
        materialsHindi: 'जूट, कपास का धागा',
        colors: 'Natural, Red, Blue'
      },
      {
        name: 'Mithila Wall Hanging',
        nameHindi: 'मिथिला दीवार लटकाने वाला',
        description: 'Decorative wall hanging with Mithila art',
        descriptionHindi: 'मिथिला कला के साथ सजावटी दीवार लटकाने वाला',
        categoryName: 'Home Decor',
        artistName: 'Kamala Devi',
        sku: 'DEC-KAM-001',
        costPrice: 400,
        sellingPrice: 800,
        mrp: 1000,
        dimensions: '25x35 cm',
        materials: 'Cloth, Natural colors',
        materialsHindi: 'कपड़ा, प्राकृतिक रंग',
        colors: 'Multi-color'
      },
      {
        name: 'Traditional Mithila Necklace',
        nameHindi: 'पारंपरिक मिथिला हार',
        description: 'Beautiful traditional necklace with Mithila designs',
        descriptionHindi: 'मिथिला डिजाइन के साथ सुंदर पारंपरिक हार',
        categoryName: 'Jewelry',
        artistName: 'Sunita Devi',
        sku: 'JEW-SUN-001',
        costPrice: 1200,
        sellingPrice: 2000,
        mrp: 2500,
        materials: 'Beads, Thread, Metal',
        materialsHindi: 'मोती, धागा, धातु',
        colors: 'Gold, Red, Green'
      }
    ];

    for (const product of products) {
      const existing = await databaseService.queryOne(
        'SELECT id FROM products WHERE sku = ?',
        [product.sku]
      );

      if (!existing.success || !existing.data) {
        // Get category and artist IDs
        const categoryResult = await databaseService.queryOne(
          'SELECT id FROM categories WHERE name = ?',
          [product.categoryName]
        );
        const artistResult = await databaseService.queryOne(
          'SELECT id FROM artists WHERE name = ?',
          [product.artistName]
        );

        if (categoryResult.data && artistResult.data) {
          await databaseService.execute(`
            INSERT INTO products (
              name, name_hindi, description, description_hindi, category_id, artist_id,
              sku, cost_price, selling_price, mrp, dimensions, materials, materials_hindi,
              colors, is_active
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
          `, [
            product.name, product.nameHindi, product.description, product.descriptionHindi,
            categoryResult.data.id, artistResult.data.id, product.sku,
            product.costPrice, product.sellingPrice, product.mrp,
            product.dimensions || null, product.materials, product.materialsHindi,
            product.colors
          ]);
        }
      }
    }
  }

  /**
   * Seed customers
   */
  private async seedCustomers(): Promise<void> {
    console.log('👥 Seeding customers...');

    const customers = [
      {
        customerCode: 'CUST001',
        name: 'Rajesh Kumar Singh',
        nameHindi: 'राजेश कुमार सिंह',
        email: '<EMAIL>',
        phone: '+91-9876543230',
        address: '123 Main Street, Patna',
        addressHindi: '123 मुख्य सड़क, पटना',
        city: 'Patna',
        state: 'Bihar',
        pincode: '800001',
        customerType: 'individual'
      },
      {
        customerCode: 'CUST002',
        name: 'Priya Sharma',
        nameHindi: 'प्रिया शर्मा',
        email: '<EMAIL>',
        phone: '+91-9876543231',
        address: '456 Park Road, Darbhanga',
        addressHindi: '456 पार्क रोड, दरभंगा',
        city: 'Darbhanga',
        state: 'Bihar',
        pincode: '846004',
        customerType: 'individual'
      },
      {
        customerCode: 'CUST003',
        name: 'Mithila Art Gallery',
        nameHindi: 'मिथिला आर्ट गैलरी',
        email: '<EMAIL>',
        phone: '+91-9876543232',
        address: '789 Art Street, Delhi',
        addressHindi: '789 आर्ट स्ट्रीट, दिल्ली',
        city: 'Delhi',
        state: 'Delhi',
        pincode: '110001',
        customerType: 'business'
      }
    ];

    for (const customer of customers) {
      const existing = await databaseService.queryOne(
        'SELECT id FROM customers WHERE customer_code = ?',
        [customer.customerCode]
      );

      if (!existing.success || !existing.data) {
        await databaseService.execute(`
          INSERT INTO customers (
            customer_code, name, name_hindi, email, phone, address, address_hindi,
            city, state, pincode, country, customer_type, loyalty_points, 
            total_purchases, is_active
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'India', ?, 0, 0.0, 1)
        `, [
          customer.customerCode, customer.name, customer.nameHindi,
          customer.email, customer.phone, customer.address, customer.addressHindi,
          customer.city, customer.state, customer.pincode, customer.customerType
        ]);
      }
    }
  }

  /**
   * Seed inventory
   */
  private async seedInventory(): Promise<void> {
    console.log('📦 Seeding inventory...');

    // Get all products and create inventory records
    const productsResult = await databaseService.query('SELECT id FROM products');
    
    if (productsResult.success && productsResult.data) {
      for (const product of productsResult.data) {
        const existing = await databaseService.queryOne(
          'SELECT id FROM inventory WHERE product_id = ?',
          [product.id]
        );

        if (!existing.success || !existing.data) {
          const currentStock = Math.floor(Math.random() * 50) + 10; // Random stock between 10-60
          const minStockLevel = 5;
          const maxStockLevel = 100;
          const reorderPoint = 10;

          await databaseService.execute(`
            INSERT INTO inventory (
              product_id, current_stock, reserved_stock, available_stock,
              min_stock_level, max_stock_level, reorder_point
            ) VALUES (?, ?, 0, ?, ?, ?, ?)
          `, [
            product.id, currentStock, currentStock,
            minStockLevel, maxStockLevel, reorderPoint
          ]);
        }
      }
    }
  }

  /**
   * Check if data seeding is needed
   */
  async isDataSeedingNeeded(): Promise<boolean> {
    try {
      const userCount = await databaseService.queryOne<{ count: number }>('SELECT COUNT(*) as count FROM users');
      const categoryCount = await databaseService.queryOne<{ count: number }>('SELECT COUNT(*) as count FROM categories');
      const artistCount = await databaseService.queryOne<{ count: number }>('SELECT COUNT(*) as count FROM artists');
      const productCount = await databaseService.queryOne<{ count: number }>('SELECT COUNT(*) as count FROM products');

      return (
        (userCount.data?.count || 0) === 0 ||
        (categoryCount.data?.count || 0) === 0 ||
        (artistCount.data?.count || 0) === 0 ||
        (productCount.data?.count || 0) === 0
      );
    } catch (error) {
      console.error('Error checking if seeding is needed:', error);
      return true; // Assume seeding is needed if we can't check
    }
  }
}

export const seedDataService = new SeedDataService();
