import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { User } from '../../../shared/types/database';

interface AuthState {
  user: Omit<User, 'passwordHash' | 'salt'> | null;
  sessionId: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  lastLoginAt: string | null;
  permissions: string[];
}

const initialState: AuthState = {
  user: null,
  sessionId: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  lastLoginAt: null,
  permissions: [],
};

// Async thunks for authentication
export const loginUser = createAsyncThunk(
  'auth/loginUser',
  async ({ username, password }: { username: string; password: string }, { rejectWithValue }) => {
    try {
      const result = await window.electronAPI.auth.login(username, password);
      if (result && result.user && result.sessionId) {
        // Store session ID in localStorage for persistence
        localStorage.setItem('sessionId', result.sessionId);
        return result;
      } else {
        return rejectWithValue('Invalid credentials');
      }
    } catch (error) {
      return rejectWithValue('Login failed: ' + String(error));
    }
  }
);

export const logoutUser = createAsyncThunk(
  'auth/logoutUser',
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { auth: AuthState };
      const sessionId = state.auth.sessionId || localStorage.getItem('sessionId');

      if (sessionId) {
        await window.electronAPI.auth.logout(sessionId);
        localStorage.removeItem('sessionId');
      }
      return true;
    } catch (error) {
      return rejectWithValue('Logout failed: ' + String(error));
    }
  }
);

export const checkAuthStatus = createAsyncThunk(
  'auth/checkAuthStatus',
  async (_, { rejectWithValue }) => {
    try {
      const sessionId = localStorage.getItem('sessionId');
      if (!sessionId) {
        return rejectWithValue('No session found');
      }

      const result = await window.electronAPI.auth.validateSession(sessionId);
      if (result && result.isValid && result.user) {
        return { user: result.user, sessionId, permissions: result.permissions || [] };
      } else {
        localStorage.removeItem('sessionId');
        return rejectWithValue('Session expired');
      }
    } catch (error) {
      localStorage.removeItem('sessionId');
      return rejectWithValue('Auth check failed: ' + String(error));
    }
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updatePermissions: (state, action: PayloadAction<string[]>) => {
      state.permissions = action.payload;
    },
    clearAuth: (state) => {
      state.user = null;
      state.sessionId = null;
      state.isAuthenticated = false;
      state.lastLoginAt = null;
      state.permissions = [];
      state.error = null;
      localStorage.removeItem('sessionId');
    },
  },
  extraReducers: (builder) => {
    // Login user
    builder
      .addCase(loginUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.sessionId = action.payload.sessionId;
        state.isAuthenticated = true;
        state.error = null;
        state.lastLoginAt = new Date().toISOString();
        state.permissions = action.payload.permissions || [];
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.isAuthenticated = false;
        state.user = null;
        state.sessionId = null;
        state.permissions = [];
      });

    // Logout user
    builder
      .addCase(logoutUser.fulfilled, (state) => {
        state.user = null;
        state.sessionId = null;
        state.isAuthenticated = false;
        state.lastLoginAt = null;
        state.permissions = [];
        state.error = null;
      });

    // Check auth status
    builder
      .addCase(checkAuthStatus.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(checkAuthStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.sessionId = action.payload.sessionId;
        state.isAuthenticated = true;
        state.permissions = action.payload.permissions;
        state.error = null;
      })
      .addCase(checkAuthStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.isAuthenticated = false;
        state.user = null;
        state.sessionId = null;
        state.permissions = [];
        localStorage.removeItem('sessionId');
      });
  },
});

export const { clearError, updatePermissions, clearAuth } = authSlice.actions;
export default authSlice.reducer;
