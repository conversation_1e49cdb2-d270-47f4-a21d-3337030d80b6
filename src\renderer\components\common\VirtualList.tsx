import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { makeStyles } from '@fluentui/react-components';

interface VirtualListProps<T> {
  items: T[];
  itemHeight: number;
  containerHeight: number;
  renderItem: (item: T, index: number) => React.ReactNode;
  overscan?: number;
  className?: string;
}

const useStyles = makeStyles({
  container: {
    overflow: 'auto',
    position: 'relative',
  },
  viewport: {
    position: 'relative',
  },
  item: {
    position: 'absolute',
    top: '0',
    left: '0',
    right: '0',
  },
});

/**
 * Virtual scrolling component for large lists
 * Optimizes performance by only rendering visible items
 */
function VirtualList<T>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  overscan = 5,
  className = '',
}: VirtualListProps<T>) {
  const styles = useStyles();
  const [scrollTop, setScrollTop] = useState(0);

  // Calculate visible range
  const visibleRange = useMemo(() => {
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
    const endIndex = Math.min(
      items.length - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
    );
    return { startIndex, endIndex };
  }, [scrollTop, itemHeight, containerHeight, items.length, overscan]);

  // Handle scroll events
  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(event.currentTarget.scrollTop);
  }, []);

  // Calculate total height
  const totalHeight = items.length * itemHeight;

  // Generate visible items
  const visibleItems = useMemo(() => {
    const result = [];
    for (let i = visibleRange.startIndex; i <= visibleRange.endIndex; i++) {
      if (items[i]) {
        result.push({
          index: i,
          item: items[i],
          top: i * itemHeight,
        });
      }
    }
    return result;
  }, [items, visibleRange, itemHeight]);

  return (
    <div
      className={`${styles.container} ${className}`}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div
        className={styles.viewport}
        style={{ height: totalHeight }}
      >
        {visibleItems.map(({ index, item, top }) => (
          <div
            key={index}
            className={styles.item}
            style={{ 
              top: `${top}px`,
              height: `${itemHeight}px`,
            }}
          >
            {renderItem(item, index)}
          </div>
        ))}
      </div>
    </div>
  );
}

export default React.memo(VirtualList) as <T>(props: VirtualListProps<T>) => JSX.Element;
