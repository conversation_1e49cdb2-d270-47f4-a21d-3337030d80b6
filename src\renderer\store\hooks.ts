import { useDispatch, useSelector, TypedUseSelectorHook } from 'react-redux';
import type { RootState, AppDispatch } from './index';

/**
 * Typed hooks for Redux store
 * Use these instead of plain `useDispatch` and `useSelector`
 */

// Use throughout your app instead of plain `useDispatch` and `useSelector`
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// Convenience selectors
export const useAuth = () => useAppSelector(state => state.auth);
export const useUI = () => useAppSelector(state => state.ui);
export const useProducts = () => useAppSelector(state => state.products);
export const useCustomers = () => useAppSelector(state => state.customers);
export const useOrders = () => useAppSelector(state => state.orders);
export const useInventory = () => useAppSelector(state => state.inventory);
