import * as crypto from 'crypto';
import { databaseService } from '../database/database';
import { User, QueryResult, PaginatedResult, SearchFilters, SortOptions } from '../../shared/types/database';

/**
 * Password security configuration
 */
interface PasswordPolicy {
  minLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSpecialChars: boolean;
  maxAttempts: number;
  lockoutDuration: number; // in minutes
}

/**
 * User service for authentication and user management
 */
export class UserService {
  private readonly passwordPolicy: PasswordPolicy = {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    maxAttempts: 5,
    lockoutDuration: 30,
  };

  private readonly failedAttempts: Map<string, { count: number; lastAttempt: Date }> = new Map();
  
  /**
   * Validate password against security policy
   */
  validatePassword(password: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (password.length < this.passwordPolicy.minLength) {
      errors.push(`Password must be at least ${this.passwordPolicy.minLength} characters long`);
    }

    if (this.passwordPolicy.requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (this.passwordPolicy.requireLowercase && !/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    if (this.passwordPolicy.requireNumbers && !/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    if (this.passwordPolicy.requireSpecialChars && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    // Check for common weak passwords
    const commonPasswords = ['password', '123456', 'admin', 'qwerty', 'letmein'];
    if (commonPasswords.includes(password.toLowerCase())) {
      errors.push('Password is too common and easily guessable');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Check if user account is locked due to failed attempts
   */
  isAccountLocked(username: string): boolean {
    const attempts = this.failedAttempts.get(username);
    if (!attempts) return false;

    const lockoutTime = this.passwordPolicy.lockoutDuration * 60 * 1000; // Convert to milliseconds
    const timeSinceLastAttempt = Date.now() - attempts.lastAttempt.getTime();

    if (attempts.count >= this.passwordPolicy.maxAttempts && timeSinceLastAttempt < lockoutTime) {
      return true;
    }

    // Reset attempts if lockout period has passed
    if (timeSinceLastAttempt >= lockoutTime) {
      this.failedAttempts.delete(username);
    }

    return false;
  }

  /**
   * Record failed login attempt
   */
  recordFailedAttempt(username: string): void {
    const attempts = this.failedAttempts.get(username) || { count: 0, lastAttempt: new Date() };
    attempts.count++;
    attempts.lastAttempt = new Date();
    this.failedAttempts.set(username, attempts);

    console.log(`⚠️ Failed login attempt for user: ${username} (${attempts.count}/${this.passwordPolicy.maxAttempts})`);
  }

  /**
   * Clear failed attempts for successful login
   */
  clearFailedAttempts(username: string): void {
    this.failedAttempts.delete(username);
  }

  /**
   * Create a new user
   */
  async createUser(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt' | 'passwordHash' | 'salt'> & { password: string }): Promise<QueryResult<User>> {
    try {
      // Validate password
      const passwordValidation = this.validatePassword(userData.password);
      if (!passwordValidation.isValid) {
        return {
          success: false,
          error: `Password validation failed: ${passwordValidation.errors.join(', ')}`
        };
      }

      // Generate salt and hash password
      const salt = crypto.randomBytes(32).toString('hex');
      const passwordHash = this.hashPassword(userData.password, salt);

      const result = databaseService.execute(`
        INSERT INTO users (username, email, password_hash, salt, role, full_name, phone, is_active)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        userData.username,
        userData.email,
        passwordHash,
        salt,
        userData.role,
        userData.fullName,
        userData.phone || null,
        userData.isActive ? 1 : 0
      ]);

      if (!result.success || !result.data) {
        return { success: false, error: result.error || 'Unknown error' };
      }

      // Get the created user
      const createdUser = await this.getUserById(result.data.lastInsertRowid as number);
      return createdUser;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create user'
      };
    }
  }

  /**
   * Get user by ID
   */
  async getUserById(id: number): Promise<QueryResult<User>> {
    const result = databaseService.queryOne<any>(`
      SELECT id, username, email, password_hash, salt, role, full_name, phone,
             is_active, last_login_at, created_at, updated_at
      FROM users WHERE id = ?
    `, [id]);

    if (result.success && result.data) {
      // Convert snake_case to camelCase and handle type conversions
      const user = result.data;
      result.data = {
        id: user.id,
        username: user.username,
        email: user.email,
        passwordHash: user.password_hash,
        salt: user.salt,
        role: user.role,
        fullName: user.full_name,
        phone: user.phone,
        isActive: Boolean(user.is_active),
        lastLoginAt: user.last_login_at,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      };
    }

    return result as QueryResult<User>;
  }

  /**
   * Get user by username
   */
  async getUserByUsername(username: string): Promise<QueryResult<User>> {
    const result = databaseService.queryOne<any>(`
      SELECT id, username, email, password_hash, salt, role, full_name, phone,
             is_active, last_login_at, created_at, updated_at
      FROM users WHERE username = ?
    `, [username]);

    if (result.success && result.data) {
      // Convert snake_case to camelCase and handle type conversions
      const user = result.data;
      result.data = {
        id: user.id,
        username: user.username,
        email: user.email,
        passwordHash: user.password_hash,
        salt: user.salt,
        role: user.role,
        fullName: user.full_name,
        phone: user.phone,
        isActive: Boolean(user.is_active),
        lastLoginAt: user.last_login_at,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      };
    }

    return result as QueryResult<User>;
  }

  /**
   * Get user by email
   */
  async getUserByEmail(email: string): Promise<QueryResult<User>> {
    const result = databaseService.queryOne<any>(`
      SELECT id, username, email, password_hash, salt, role, full_name, phone,
             is_active, last_login_at, created_at, updated_at
      FROM users WHERE email = ?
    `, [email]);

    if (result.success && result.data) {
      // Convert snake_case to camelCase and handle type conversions
      const user = result.data;
      result.data = {
        id: user.id,
        username: user.username,
        email: user.email,
        passwordHash: user.password_hash,
        salt: user.salt,
        role: user.role,
        fullName: user.full_name,
        phone: user.phone,
        isActive: Boolean(user.is_active),
        lastLoginAt: user.last_login_at,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      };
    }

    return result as QueryResult<User>;
  }

  /**
   * Get all users with pagination and filtering
   */
  async getUsers(filters?: SearchFilters, sort?: SortOptions, page: number = 1, limit: number = 50): Promise<QueryResult<PaginatedResult<User>>> {
    let sql = `
      SELECT id, username, email, password_hash, salt, role, full_name, phone, 
             is_active, last_login_at, created_at, updated_at
      FROM users
    `;

    let params: any[] = [];

    if (filters) {
      const { where, params: whereParams } = databaseService.buildWhereClause(filters);
      sql += ` ${where}`;
      params = whereParams;
    }

    sql += ` ${databaseService.buildOrderClause(sort)}`;

    const result = await databaseService.paginate<any>(sql, params, page, limit);

    if (result.success && result.data && result.data.data) {
      // Convert snake_case to camelCase and handle type conversions
      result.data.data = result.data.data.map((user: any) => ({
        id: user.id,
        username: user.username,
        email: user.email,
        passwordHash: user.password_hash,
        salt: user.salt,
        role: user.role,
        fullName: user.full_name,
        phone: user.phone,
        isActive: Boolean(user.is_active),
        lastLoginAt: user.last_login_at,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      }));
    }

    return result as QueryResult<PaginatedResult<User>>;
  }

  /**
   * Update user
   */
  async updateUser(id: number, userData: Partial<Omit<User, 'id' | 'createdAt' | 'updatedAt' | 'passwordHash' | 'salt'>>): Promise<QueryResult<User>> {
    try {
      const updates: string[] = [];
      const params: any[] = [];

      if (userData.username !== undefined) {
        updates.push('username = ?');
        params.push(userData.username);
      }

      if (userData.email !== undefined) {
        updates.push('email = ?');
        params.push(userData.email);
      }

      if (userData.role !== undefined) {
        updates.push('role = ?');
        params.push(userData.role);
      }

      if (userData.fullName !== undefined) {
        updates.push('full_name = ?');
        params.push(userData.fullName);
      }

      if (userData.phone !== undefined) {
        updates.push('phone = ?');
        params.push(userData.phone);
      }

      if (userData.isActive !== undefined) {
        updates.push('is_active = ?');
        params.push(userData.isActive ? 1 : 0);
      }

      if (updates.length === 0) {
        return { success: false, error: 'No fields to update' };
      }

      updates.push('updated_at = datetime("now")');
      params.push(id);

      const result = databaseService.execute(`
        UPDATE users SET ${updates.join(', ')} WHERE id = ?
      `, params);

      if (!result.success) {
        return { success: false, error: result.error || 'Unknown error' };
      }

      // Get the updated user
      return await this.getUserById(id);
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update user'
      };
    }
  }

  /**
   * Update user password
   */
  async updatePassword(id: number, newPassword: string): Promise<QueryResult<boolean>> {
    try {
      const salt = crypto.randomBytes(32).toString('hex');
      const passwordHash = this.hashPassword(newPassword, salt);

      const result = databaseService.execute(`
        UPDATE users SET password_hash = ?, salt = ?, updated_at = datetime('now') WHERE id = ?
      `, [passwordHash, salt, id]);

      return {
        success: result.success,
        data: result.success,
        error: result.error || undefined
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update password'
      };
    }
  }

  /**
   * Delete user (soft delete by setting inactive)
   */
  async deleteUser(id: number): Promise<QueryResult<boolean>> {
    const result = databaseService.execute(`
      UPDATE users SET is_active = 0, updated_at = datetime('now') WHERE id = ?
    `, [id]);

    return {
      success: result.success,
      data: result.success,
      error: result.error || undefined
    };
  }

  /**
   * Authenticate user (alias for authenticate method)
   */
  async authenticateUser(username: string, password: string): Promise<QueryResult<User | null>> {
    return this.authenticate(username, password);
  }

  /**
   * Authenticate user
   */
  async authenticate(username: string, password: string): Promise<QueryResult<User | null>> {
    try {
      console.log(`🔐 Authenticating user: ${username}`);

      // Check if account is locked
      if (this.isAccountLocked(username)) {
        const lockoutMinutes = this.passwordPolicy.lockoutDuration;
        console.log(`🔒 Account locked: ${username}`);
        return {
          success: false,
          error: `Account is locked due to too many failed attempts. Please try again in ${lockoutMinutes} minutes.`
        };
      }

      const userResult = await this.getUserByUsername(username);
      console.log(`👤 User lookup result:`, { success: userResult.success, hasData: !!userResult.data });

      if (!userResult.success || !userResult.data) {
        console.log(`❌ User not found: ${username}`);
        this.recordFailedAttempt(username);
        return { success: true, data: null }; // User not found
      }

      const user = userResult.data;
      console.log(`👤 User found:`, {
        id: user.id,
        username: user.username,
        isActive: user.isActive,
        hasPasswordHash: !!user.passwordHash,
        hasSalt: !!user.salt
      });

      if (!user.isActive) {
        console.log(`🚫 User account inactive: ${username}`);
        this.recordFailedAttempt(username);
        return { success: false, error: 'User account is inactive' };
      }

      const hashedPassword = this.hashPassword(password, user.salt);
      console.log(`🔑 Password verification:`, {
        providedPasswordLength: password.length,
        hashedPasswordLength: hashedPassword.length,
        storedPasswordHashLength: user.passwordHash.length,
        passwordsMatch: hashedPassword === user.passwordHash
      });

      if (hashedPassword !== user.passwordHash) {
        console.log(`❌ Invalid password for user: ${username}`);
        this.recordFailedAttempt(username);
        return { success: true, data: null }; // Invalid password
      }

      console.log(`✅ Authentication successful for user: ${username}`);

      // Clear failed attempts on successful login
      this.clearFailedAttempts(username);

      // Update last login time
      await databaseService.execute(`
        UPDATE users SET last_login_at = datetime('now') WHERE id = ?
      `, [user.id]);

      // Remove sensitive data before returning
      const { passwordHash, salt, ...safeUser } = user;

      return { success: true, data: safeUser as User };
    } catch (error) {
      console.log(`💥 Authentication error:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Authentication failed'
      };
    }
  }

  /**
   * Hash password with salt
   */
  private hashPassword(password: string, salt: string): string {
    return crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');
  }

  /**
   * Create default admin user if no users exist
   */
  async createDefaultAdmin(): Promise<QueryResult<User | null>> {
    try {
      // Check if admin user exists
      const adminResult = await this.getUserByUsername('admin');

      if (adminResult.success && adminResult.data) {
        // Admin exists, ensure it's active and has the correct password
        console.log('👤 Admin user exists, ensuring it is active and has correct password...');

        // Generate new salt and hash for the expected password
        const salt = crypto.randomBytes(32).toString('hex');
        const passwordHash = this.hashPassword('Admin123!', salt);

        const updateResult = databaseService.execute(
          'UPDATE users SET is_active = 1, password_hash = ?, salt = ?, updated_at = datetime(\'now\') WHERE username = ?',
          [passwordHash, salt, 'admin']
        );

        if (updateResult.success) {
          console.log('✅ Admin user activated and password updated');
          return await this.getUserByUsername('admin');
        } else {
          console.log('❌ Failed to activate admin user');
        }

        return adminResult;
      }

      // Create default admin user
      console.log('👤 Creating default admin user...');
      const defaultAdmin = {
        username: 'admin',
        email: '<EMAIL>',
        password: 'Admin123!', // Meets password requirements: uppercase, lowercase, number, special char
        role: 'admin' as const,
        fullName: 'System Administrator',
        isActive: true
      };

      const result = await this.createUser(defaultAdmin);
      if (result.success) {
        console.log('✅ Default admin user created successfully');
      } else {
        console.log('❌ Failed to create default admin user:', result.error);
      }

      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create default admin'
      };
    }
  }
}

export const userService = new UserService();
