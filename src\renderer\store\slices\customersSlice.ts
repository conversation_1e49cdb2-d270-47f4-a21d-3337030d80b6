import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Customer } from '../../../shared/types/database';

interface CustomersState {
  selectedCustomer: Customer | null;
  recentCustomers: Customer[];
  sortBy: 'name' | 'email' | 'created_at' | 'total_orders';
  sortOrder: 'asc' | 'desc';
  viewMode: 'grid' | 'list' | 'table';
  pageSize: number;
  currentPage: number;
}

const initialState: CustomersState = {
  selectedCustomer: null,
  recentCustomers: [],
  sortBy: 'created_at',
  sortOrder: 'desc',
  viewMode: 'table',
  pageSize: 25,
  currentPage: 1,
};

const customersSlice = createSlice({
  name: 'customers',
  initialState,
  reducers: {
    setSelectedCustomer: (state, action: PayloadAction<Customer | null>) => {
      state.selectedCustomer = action.payload;
      
      if (action.payload) {
        const existingIndex = state.recentCustomers.findIndex(c => c.id === action.payload!.id);
        if (existingIndex >= 0) {
          state.recentCustomers.splice(existingIndex, 1);
        }
        state.recentCustomers.unshift(action.payload);
        
        if (state.recentCustomers.length > 5) {
          state.recentCustomers = state.recentCustomers.slice(0, 5);
        }
      }
    },
    setSortBy: (state, action: PayloadAction<CustomersState['sortBy']>) => {
      state.sortBy = action.payload;
    },
    setSortOrder: (state, action: PayloadAction<CustomersState['sortOrder']>) => {
      state.sortOrder = action.payload;
    },
    setViewMode: (state, action: PayloadAction<CustomersState['viewMode']>) => {
      state.viewMode = action.payload;
    },
    setPageSize: (state, action: PayloadAction<number>) => {
      state.pageSize = action.payload;
      state.currentPage = 1;
    },
    setCurrentPage: (state, action: PayloadAction<number>) => {
      state.currentPage = action.payload;
    },
  },
});

export const {
  setSelectedCustomer,
  setSortBy,
  setSortOrder,
  setViewMode,
  setPageSize,
  setCurrentPage,
} = customersSlice.actions;

export default customersSlice.reducer;
