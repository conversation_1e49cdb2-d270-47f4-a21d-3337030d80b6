import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Product } from '../../../shared/types/database';

interface ProductsState {
  selectedProduct: Product | null;
  recentlyViewed: Product[];
  favorites: number[];
  bulkSelection: number[];
  sortBy: 'name' | 'price' | 'created_at' | 'stock_quantity';
  sortOrder: 'asc' | 'desc';
  viewMode: 'grid' | 'list' | 'table';
  pageSize: number;
  currentPage: number;
}

const initialState: ProductsState = {
  selectedProduct: null,
  recentlyViewed: [],
  favorites: [],
  bulkSelection: [],
  sortBy: 'created_at',
  sortOrder: 'desc',
  viewMode: 'grid',
  pageSize: 20,
  currentPage: 1,
};

const productsSlice = createSlice({
  name: 'products',
  initialState,
  reducers: {
    setSelectedProduct: (state, action: PayloadAction<Product | null>) => {
      state.selectedProduct = action.payload;
      
      // Add to recently viewed if it's a valid product
      if (action.payload) {
        const existingIndex = state.recentlyViewed.findIndex(p => p.id === action.payload!.id);
        if (existingIndex >= 0) {
          // Move to front if already exists
          state.recentlyViewed.splice(existingIndex, 1);
        }
        state.recentlyViewed.unshift(action.payload);
        
        // Keep only last 10 recently viewed
        if (state.recentlyViewed.length > 10) {
          state.recentlyViewed = state.recentlyViewed.slice(0, 10);
        }
      }
    },
    
    addToFavorites: (state, action: PayloadAction<number>) => {
      if (!state.favorites.includes(action.payload)) {
        state.favorites.push(action.payload);
      }
    },
    
    removeFromFavorites: (state, action: PayloadAction<number>) => {
      state.favorites = state.favorites.filter(id => id !== action.payload);
    },
    
    toggleFavorite: (state, action: PayloadAction<number>) => {
      const productId = action.payload;
      if (state.favorites.includes(productId)) {
        state.favorites = state.favorites.filter(id => id !== productId);
      } else {
        state.favorites.push(productId);
      }
    },
    
    setBulkSelection: (state, action: PayloadAction<number[]>) => {
      state.bulkSelection = action.payload;
    },
    
    addToBulkSelection: (state, action: PayloadAction<number>) => {
      if (!state.bulkSelection.includes(action.payload)) {
        state.bulkSelection.push(action.payload);
      }
    },
    
    removeFromBulkSelection: (state, action: PayloadAction<number>) => {
      state.bulkSelection = state.bulkSelection.filter(id => id !== action.payload);
    },
    
    toggleBulkSelection: (state, action: PayloadAction<number>) => {
      const productId = action.payload;
      if (state.bulkSelection.includes(productId)) {
        state.bulkSelection = state.bulkSelection.filter(id => id !== productId);
      } else {
        state.bulkSelection.push(productId);
      }
    },
    
    clearBulkSelection: (state) => {
      state.bulkSelection = [];
    },
    
    setSortBy: (state, action: PayloadAction<ProductsState['sortBy']>) => {
      state.sortBy = action.payload;
    },
    
    setSortOrder: (state, action: PayloadAction<ProductsState['sortOrder']>) => {
      state.sortOrder = action.payload;
    },
    
    toggleSortOrder: (state) => {
      state.sortOrder = state.sortOrder === 'asc' ? 'desc' : 'asc';
    },
    
    setViewMode: (state, action: PayloadAction<ProductsState['viewMode']>) => {
      state.viewMode = action.payload;
    },
    
    setPageSize: (state, action: PayloadAction<number>) => {
      state.pageSize = action.payload;
      state.currentPage = 1; // Reset to first page when changing page size
    },
    
    setCurrentPage: (state, action: PayloadAction<number>) => {
      state.currentPage = action.payload;
    },
    
    clearRecentlyViewed: (state) => {
      state.recentlyViewed = [];
    },
    
    resetProductsState: (state) => {
      return { ...initialState, favorites: state.favorites }; // Keep favorites
    },
  },
});

export const {
  setSelectedProduct,
  addToFavorites,
  removeFromFavorites,
  toggleFavorite,
  setBulkSelection,
  addToBulkSelection,
  removeFromBulkSelection,
  toggleBulkSelection,
  clearBulkSelection,
  setSortBy,
  setSortOrder,
  toggleSortOrder,
  setViewMode,
  setPageSize,
  setCurrentPage,
  clearRecentlyViewed,
  resetProductsState,
} = productsSlice.actions;

export default productsSlice.reducer;
