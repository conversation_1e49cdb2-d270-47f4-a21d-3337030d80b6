/* Global styles for Maithili Shop Management System */

* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  height: 100%;
  overflow: hidden; /* Prevent scrollbars in Electron */
}

#root {
  height: 100vh;
  width: 100vw;
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--colorNeutralBackground2);
}

::-webkit-scrollbar-thumb {
  background: var(--colorNeutralStroke1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--colorNeutralStroke2);
}

/* Mithila-inspired accent colors */
:root {
  --mithila-red: #d32f2f;
  --mithila-orange: #ff9800;
  --mithila-yellow: #ffc107;
  --mithila-green: #388e3c;
  --mithila-blue: #1976d2;
}

/* Custom focus styles for accessibility */
*:focus-visible {
  outline: 2px solid var(--colorStrokeFocus2);
  outline-offset: 2px;
}

/* Smooth transitions for interactive elements */
button, .interactive {
  transition: all 0.2s ease-in-out;
}

/* Typography enhancements for Devanagari script */
.devanagari {
  font-family: 'Noto Sans Devanagari', 'Segoe UI', sans-serif;
  line-height: 1.6;
}

/* Utility classes */
.text-center {
  text-align: center;
}

.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.gap-small {
  gap: 8px;
}

.gap-medium {
  gap: 16px;
}

.gap-large {
  gap: 24px;
}
