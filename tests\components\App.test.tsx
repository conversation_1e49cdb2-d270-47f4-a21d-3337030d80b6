import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { FluentProvider, webLightTheme } from '@fluentui/react-components';
import { configureStore } from '@reduxjs/toolkit';
import App from '../../src/renderer/App';
import { api } from '../../src/renderer/store/api';
import authSlice from '../../src/renderer/store/slices/authSlice';
import uiSlice from '../../src/renderer/store/slices/uiSlice';
import productsSlice from '../../src/renderer/store/slices/productsSlice';
import customersSlice from '../../src/renderer/store/slices/customersSlice';
import ordersSlice from '../../src/renderer/store/slices/ordersSlice';
import inventorySlice from '../../src/renderer/store/slices/inventorySlice';

// Create a test store
const createTestStore = () => {
  return configureStore({
    reducer: {
      [api.reducerPath]: api.reducer,
      auth: authSlice,
      ui: uiSlice,
      products: productsSlice,
      customers: customersSlice,
      orders: ordersSlice,
      inventory: inventorySlice,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware().concat(api.middleware),
  });
};

const renderWithProviders = (component: React.ReactElement) => {
  const store = createTestStore();
  return render(
    <Provider store={store}>
      <FluentProvider theme={webLightTheme}>
        {component}
      </FluentProvider>
    </Provider>
  );
};

describe('App Component', () => {
  it('renders without crashing', () => {
    renderWithProviders(<App />);
    expect(screen.getByText(/मैथिली विकास कोष Shop Management/i)).toBeInTheDocument();
  });

  it('displays the welcome message', () => {
    renderWithProviders(<App />);
    expect(screen.getByText(/Welcome to Mithila Handcraft Management/i)).toBeInTheDocument();
  });

  it('displays all main feature cards', () => {
    renderWithProviders(<App />);
    
    expect(screen.getByText('Products')).toBeInTheDocument();
    expect(screen.getByText('Inventory')).toBeInTheDocument();
    expect(screen.getByText('Customers')).toBeInTheDocument();
    expect(screen.getByText('Artists')).toBeInTheDocument();
  });

  it('displays feature card descriptions', () => {
    renderWithProviders(<App />);
    
    expect(screen.getByText(/Manage bags, sarees, paintings and other handcrafts/i)).toBeInTheDocument();
    expect(screen.getByText(/Track stock levels and manage your inventory/i)).toBeInTheDocument();
    expect(screen.getByText(/Manage customer information and purchase history/i)).toBeInTheDocument();
    expect(screen.getByText(/Manage artisan profiles and track commissions/i)).toBeInTheDocument();
  });

  it('handles card clicks', () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
    
    renderWithProviders(<App />);
    
    const productsCard = screen.getByText('Products').closest('[role="button"]') || 
                        screen.getByText('Products').closest('div[class*="card"]');
    
    if (productsCard) {
      fireEvent.click(productsCard);
      expect(consoleSpy).toHaveBeenCalledWith('Navigating to products');
    }
    
    consoleSpy.mockRestore();
  });

  it('displays the settings button', () => {
    renderWithProviders(<App />);
    expect(screen.getByText('Settings')).toBeInTheDocument();
  });

  it('has proper accessibility attributes', () => {
    renderWithProviders(<App />);
    
    // Check for proper heading structure
    const mainHeading = screen.getByRole('heading', { level: 1 });
    expect(mainHeading).toBeInTheDocument();
  });

  it('applies correct CSS classes for styling', () => {
    const { container } = renderWithProviders(<App />);
    
    // Check that the main container has the expected structure
    const appContainer = container.firstChild;
    expect(appContainer).toHaveClass('container');
  });
});
