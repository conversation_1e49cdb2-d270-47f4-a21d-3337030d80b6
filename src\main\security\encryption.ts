import * as crypto from 'crypto';
import * as fs from 'fs';
import * as path from 'path';
import { app } from 'electron';

/**
 * Encryption service for Maithili Vikas Kosh Shop Management System
 * Provides AES-256 encryption for sensitive data and database files
 */
export class EncryptionService {
  private static instance: EncryptionService;
  private encryptionKey: Buffer | null = null;
  private readonly algorithm = 'aes-256-gcm';
  private readonly keyFile: string;

  constructor() {
    // Store encryption key in user data directory
    const userDataPath = app.getPath('userData');
    this.keyFile = path.join(userDataPath, '.encryption-key');
  }

  static getInstance(): EncryptionService {
    if (!EncryptionService.instance) {
      EncryptionService.instance = new EncryptionService();
    }
    return EncryptionService.instance;
  }

  /**
   * Initialize encryption service
   */
  async initialize(): Promise<void> {
    try {
      await this.loadOrCreateEncryptionKey();
      console.log('🔐 Encryption service initialized');
    } catch (error) {
      console.error('❌ Failed to initialize encryption service:', error);
      throw error;
    }
  }

  /**
   * Load existing encryption key or create a new one
   */
  private async loadOrCreateEncryptionKey(): Promise<void> {
    try {
      if (fs.existsSync(this.keyFile)) {
        // Load existing key
        const keyData = fs.readFileSync(this.keyFile);
        this.encryptionKey = keyData;
        console.log('🔑 Loaded existing encryption key');
      } else {
        // Create new key
        this.encryptionKey = crypto.randomBytes(32); // 256-bit key
        
        // Ensure directory exists
        const keyDir = path.dirname(this.keyFile);
        if (!fs.existsSync(keyDir)) {
          fs.mkdirSync(keyDir, { recursive: true });
        }
        
        // Save key to file with restricted permissions
        fs.writeFileSync(this.keyFile, this.encryptionKey, { mode: 0o600 });
        console.log('🔑 Created new encryption key');
      }
    } catch (error) {
      console.error('❌ Failed to load/create encryption key:', error);
      throw error;
    }
  }

  /**
   * Encrypt data using AES-256-GCM
   */
  encrypt(data: string): { encrypted: string; iv: string; tag: string } {
    if (!this.encryptionKey) {
      throw new Error('Encryption service not initialized');
    }

    const iv = crypto.randomBytes(16); // 128-bit IV
    const cipher = crypto.createCipheriv(this.algorithm, this.encryptionKey, iv);
    cipher.setAAD(Buffer.from('maithili-shop', 'utf8'));

    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    const tag = cipher.getAuthTag();

    return {
      encrypted,
      iv: iv.toString('hex'),
      tag: tag.toString('hex')
    };
  }

  /**
   * Decrypt data using AES-256-GCM
   */
  decrypt(encryptedData: { encrypted: string; iv: string; tag: string }): string {
    if (!this.encryptionKey) {
      throw new Error('Encryption service not initialized');
    }

    const iv = Buffer.from(encryptedData.iv, 'hex');
    const decipher = crypto.createDecipheriv(this.algorithm, this.encryptionKey, iv);
    decipher.setAAD(Buffer.from('maithili-shop', 'utf8'));
    decipher.setAuthTag(Buffer.from(encryptedData.tag, 'hex'));

    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  }

  /**
   * Hash password with salt using PBKDF2
   */
  hashPassword(password: string, salt?: string): { hash: string; salt: string } {
    const passwordSalt = salt || crypto.randomBytes(32).toString('hex');
    const hash = crypto.pbkdf2Sync(password, passwordSalt, 10000, 64, 'sha512').toString('hex');
    
    return {
      hash,
      salt: passwordSalt
    };
  }

  /**
   * Verify password against hash
   */
  verifyPassword(password: string, hash: string, salt: string): boolean {
    const verifyHash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');
    return hash === verifyHash;
  }

  /**
   * Generate secure random token
   */
  generateToken(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Encrypt file
   */
  async encryptFile(inputPath: string, outputPath: string): Promise<void> {
    if (!this.encryptionKey) {
      throw new Error('Encryption service not initialized');
    }

    return new Promise((resolve, reject) => {
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipheriv(this.algorithm, this.encryptionKey!, iv);

      const input = fs.createReadStream(inputPath);
      const output = fs.createWriteStream(outputPath);

      // Write IV to the beginning of the file
      output.write(iv);

      input.pipe(cipher).pipe(output);

      output.on('finish', () => resolve());
      output.on('error', reject);
      input.on('error', reject);
    });
  }

  /**
   * Decrypt file
   */
  async decryptFile(inputPath: string, outputPath: string): Promise<void> {
    if (!this.encryptionKey) {
      throw new Error('Encryption service not initialized');
    }

    return new Promise((resolve, reject) => {
      const input = fs.createReadStream(inputPath);
      const output = fs.createWriteStream(outputPath);
      
      // Read IV from the beginning of the file
      const ivBuffer = Buffer.alloc(16);
      let ivRead = false;
      
      input.on('readable', () => {
        if (!ivRead) {
          const chunk = input.read(16);
          if (chunk) {
            chunk.copy(ivBuffer);
            ivRead = true;

            const decipher = crypto.createDecipheriv(this.algorithm, this.encryptionKey!, ivBuffer);
            input.pipe(decipher).pipe(output);
          }
        }
      });
      
      output.on('finish', () => resolve());
      output.on('error', reject);
      input.on('error', reject);
    });
  }

  /**
   * Get encryption status
   */
  isInitialized(): boolean {
    return this.encryptionKey !== null;
  }
}

// Export singleton instance
export const encryptionService = EncryptionService.getInstance();
