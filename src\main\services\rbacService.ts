import { User } from '../../shared/types/database';

/**
 * Role-based access control service
 */
export class RBACService {
  
  // Define permissions for each role
  private readonly rolePermissions: Record<string, string[]> = {
    admin: [
      // User management
      'users:create',
      'users:read',
      'users:update',
      'users:delete',
      'users:manage_roles',
      
      // Product management
      'products:create',
      'products:read',
      'products:update',
      'products:delete',
      'products:manage_categories',
      
      // Customer management
      'customers:create',
      'customers:read',
      'customers:update',
      'customers:delete',
      
      // Order management
      'orders:create',
      'orders:read',
      'orders:update',
      'orders:delete',
      'orders:process',
      
      // Inventory management
      'inventory:read',
      'inventory:update',
      'inventory:adjust',
      
      // Artist management
      'artists:create',
      'artists:read',
      'artists:update',
      'artists:delete',
      
      // Reports and analytics
      'reports:view',
      'reports:export',
      'analytics:view',
      
      // System settings
      'settings:read',
      'settings:update',
      'backup:create',
      'backup:restore',
      
      // Session management
      'sessions:view',
      'sessions:manage',
    ],
    
    staff: [
      // Product management (limited)
      'products:create',
      'products:read',
      'products:update',
      
      // Customer management
      'customers:create',
      'customers:read',
      'customers:update',
      
      // Order management
      'orders:create',
      'orders:read',
      'orders:update',
      'orders:process',
      
      // Inventory management (limited)
      'inventory:read',
      'inventory:update',
      
      // Artist management (read-only)
      'artists:read',
      
      // Basic reports
      'reports:view',
    ],
    
    viewer: [
      // Read-only access
      'products:read',
      'customers:read',
      'orders:read',
      'inventory:read',
      'artists:read',
      'reports:view',
    ],
  };

  /**
   * Check if a user has a specific permission
   */
  hasPermission(user: Pick<User, 'role' | 'isActive'>, permission: string): boolean {
    // Inactive users have no permissions
    if (!user.isActive) {
      return false;
    }

    const userPermissions = this.rolePermissions[user.role] || [];
    return userPermissions.includes(permission);
  }

  /**
   * Check if a user has any of the specified permissions
   */
  hasAnyPermission(user: Pick<User, 'role' | 'isActive'>, permissions: string[]): boolean {
    return permissions.some(permission => this.hasPermission(user, permission));
  }

  /**
   * Check if a user has all of the specified permissions
   */
  hasAllPermissions(user: Pick<User, 'role' | 'isActive'>, permissions: string[]): boolean {
    return permissions.every(permission => this.hasPermission(user, permission));
  }

  /**
   * Get all permissions for a user's role
   */
  getUserPermissions(user: Pick<User, 'role' | 'isActive'>): string[] {
    if (!user.isActive) {
      return [];
    }

    return [...(this.rolePermissions[user.role] || [])];
  }

  /**
   * Get all available roles
   */
  getAvailableRoles(): string[] {
    return Object.keys(this.rolePermissions);
  }

  /**
   * Get role hierarchy (higher number = more permissions)
   */
  getRoleLevel(role: string): number {
    const roleLevels: Record<string, number> = {
      viewer: 1,
      staff: 2,
      admin: 3,
    };

    return roleLevels[role] || 0;
  }

  /**
   * Check if a user can manage another user (based on role hierarchy)
   */
  canManageUser(manager: Pick<User, 'role' | 'isActive'>, target: Pick<User, 'role'>): boolean {
    if (!this.hasPermission(manager, 'users:manage_roles')) {
      return false;
    }

    const managerLevel = this.getRoleLevel(manager.role);
    const targetLevel = this.getRoleLevel(target.role);

    // Can only manage users with lower or equal role level
    return managerLevel >= targetLevel;
  }

  /**
   * Get permissions grouped by category
   */
  getPermissionsByCategory(): Record<string, string[]> {
    const categories: Record<string, string[]> = {};

    // Get all unique permissions
    const allPermissions = new Set<string>();
    Object.values(this.rolePermissions).forEach(permissions => {
      permissions.forEach(permission => allPermissions.add(permission));
    });

    // Group by category (prefix before colon)
    allPermissions.forEach(permission => {
      const [category] = permission.split(':');
      if (!categories[category]) {
        categories[category] = [];
      }
      categories[category].push(permission);
    });

    // Sort permissions within each category
    Object.keys(categories).forEach(category => {
      categories[category].sort();
    });

    return categories;
  }

  /**
   * Validate if a role exists
   */
  isValidRole(role: string): boolean {
    return this.getAvailableRoles().includes(role);
  }

  /**
   * Get role description
   */
  getRoleDescription(role: string): string {
    const descriptions: Record<string, string> = {
      admin: 'Full system access with user management and system configuration capabilities',
      staff: 'Standard business operations including product, customer, and order management',
      viewer: 'Read-only access to view data and reports',
    };

    return descriptions[role] || 'Unknown role';
  }

  /**
   * Create a permission check middleware function
   */
  createPermissionChecker(requiredPermission: string) {
    return (user: Pick<User, 'role' | 'isActive'> | null): boolean => {
      if (!user) {
        return false;
      }
      return this.hasPermission(user, requiredPermission);
    };
  }

  /**
   * Create a role check middleware function
   */
  createRoleChecker(requiredRole: string) {
    return (user: Pick<User, 'role' | 'isActive'> | null): boolean => {
      if (!user || !user.isActive) {
        return false;
      }
      
      const userLevel = this.getRoleLevel(user.role);
      const requiredLevel = this.getRoleLevel(requiredRole);
      
      return userLevel >= requiredLevel;
    };
  }

  /**
   * Get filtered menu items based on user permissions
   */
  getAccessibleMenuItems(user: Pick<User, 'role' | 'isActive'>): Array<{
    id: string;
    label: string;
    icon: string;
    permission: string;
    path: string;
  }> {
    const menuItems = [
      { id: 'dashboard', label: 'Dashboard', icon: 'Home', permission: 'products:read', path: '/dashboard' },
      { id: 'products', label: 'Products', icon: 'Box', permission: 'products:read', path: '/products' },
      { id: 'customers', label: 'Customers', icon: 'People', permission: 'customers:read', path: '/customers' },
      { id: 'orders', label: 'Orders', icon: 'ShoppingCart', permission: 'orders:read', path: '/orders' },
      { id: 'inventory', label: 'Inventory', icon: 'Package', permission: 'inventory:read', path: '/inventory' },
      { id: 'artists', label: 'Artists', icon: 'Brush', permission: 'artists:read', path: '/artists' },
      { id: 'reports', label: 'Reports', icon: 'BarChart', permission: 'reports:view', path: '/reports' },
      { id: 'users', label: 'User Management', icon: 'PersonSettings', permission: 'users:read', path: '/users' },
      { id: 'settings', label: 'Settings', icon: 'Settings', permission: 'settings:read', path: '/settings' },
    ];

    return menuItems.filter(item => this.hasPermission(user, item.permission));
  }
}

// Export singleton instance
export const rbacService = new RBACService();
