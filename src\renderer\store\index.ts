import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import { api } from './api';
import authSlice from './slices/authSlice';
import uiSlice from './slices/uiSlice';
import productsSlice from './slices/productsSlice';
import customersSlice from './slices/customersSlice';
import ordersSlice from './slices/ordersSlice';
import inventorySlice from './slices/inventorySlice';

/**
 * Redux store configuration for Maithili Vikas Kosh Shop Management
 * Uses Redux Toolkit with RTK Query for efficient state management
 */
export const store = configureStore({
  reducer: {
    // RTK Query API slice
    [api.reducerPath]: api.reducer,
    
    // Feature slices
    auth: authSlice,
    ui: uiSlice,
    products: productsSlice,
    customers: customersSlice,
    orders: ordersSlice,
    inventory: inventorySlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // Ignore these action types for RTK Query
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }).concat(api.middleware),
  devTools: process.env.NODE_ENV !== 'production',
});

// Enable listener behavior for the store
setupListeners(store.dispatch);

// Export types for TypeScript
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Export store as default
export default store;
