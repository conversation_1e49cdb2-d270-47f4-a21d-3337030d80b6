import { store } from '../../src/renderer/store';
import { addNotification, setTheme } from '../../src/renderer/store/slices/uiSlice';
import { setSelectedProduct } from '../../src/renderer/store/slices/productsSlice';

describe('Redux Store', () => {
  it('should have the correct initial state', () => {
    const state = store.getState();
    
    expect(state.ui.theme).toBe('auto');
    expect(state.ui.language).toBe('en');
    expect(state.ui.sidebarCollapsed).toBe(false);
    expect(state.auth.isAuthenticated).toBe(false);
    expect(state.products.selectedProduct).toBe(null);
  });

  it('should handle UI actions', () => {
    // Test theme change
    store.dispatch(setTheme('dark'));
    expect(store.getState().ui.theme).toBe('dark');

    // Test notification
    store.dispatch(addNotification({
      type: 'success',
      title: 'Test',
      message: 'Test notification',
    }));
    
    const notifications = store.getState().ui.notifications;
    expect(notifications).toHaveLength(1);
    expect(notifications[0].title).toBe('Test');
    expect(notifications[0].type).toBe('success');
  });

  it('should handle product actions', () => {
    const mockProduct = {
      id: 1,
      name: 'Test Product',
      description: 'Test Description',
      price: 100,
      category_id: 1,
      artist_id: 1,
      stock_quantity: 10,
      sku: 'TEST001',
      status: 'active' as const,
      created_at: '2024-01-01',
      updated_at: '2024-01-01',
    };

    store.dispatch(setSelectedProduct(mockProduct));
    
    const state = store.getState();
    expect(state.products.selectedProduct).toEqual(mockProduct);
    expect(state.products.recentlyViewed).toHaveLength(1);
    expect(state.products.recentlyViewed[0]).toEqual(mockProduct);
  });

  it('should maintain state consistency', () => {
    const initialState = store.getState();
    
    // Perform multiple actions
    store.dispatch(setTheme('light'));
    store.dispatch(addNotification({
      type: 'info',
      title: 'Info',
      message: 'Information message',
    }));
    
    const newState = store.getState();
    
    // Check that unrelated state hasn't changed
    expect(newState.auth).toEqual(initialState.auth);
    expect(newState.products.selectedProduct).toEqual(initialState.products.selectedProduct);
    
    // Check that related state has changed
    expect(newState.ui.theme).toBe('light');
    expect(newState.ui.notifications).toHaveLength(2); // Previous test + this one
  });
});
