import { contextBridge, ipc<PERSON>enderer } from 'electron';

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // App info
  getAppVersion: () => ipcRenderer.invoke('app:getVersion'),
  
  // Window controls
  minimizeWindow: () => ipcRenderer.invoke('window:minimize'),
  maximizeWindow: () => ipcRenderer.invoke('window:maximize'),
  closeWindow: () => ipcRenderer.invoke('window:close'),
  
  // Database operations
  database: {
    // User operations
    getUsers: () => ipcRenderer.invoke('db:getUsers'),
    createUser: (userData: any) => ipcRenderer.invoke('db:createUser', userData),

    // Product operations
    getProducts: (params?: any) => ipcRenderer.invoke('db:getProducts', params),
    getProduct: (id: number) => ipc<PERSON>ender<PERSON>.invoke('db:getProduct', id),
    createProduct: (productData: any) => ipcRenderer.invoke('db:createProduct', productData),
    updateProduct: (id: number, updates: any) => ipcRenderer.invoke('db:updateProduct', id, updates),
    deleteProduct: (id: number) => ipcRenderer.invoke('db:deleteProduct', id),

    // Category operations
    getCategories: () => ipcRenderer.invoke('db:getCategories'),

    // Customer operations
    getCustomers: (params?: any) => ipcRenderer.invoke('db:getCustomers', params),
    createCustomer: (customerData: any) => ipcRenderer.invoke('db:createCustomer', customerData),

    // Order operations
    getOrders: (params?: any) => ipcRenderer.invoke('db:getOrders', params),
    createOrder: (orderData: any) => ipcRenderer.invoke('db:createOrder', orderData),

    // Artist operations
    getArtists: () => ipcRenderer.invoke('db:getArtists'),

    // Inventory operations
    getInventory: (params?: any) => ipcRenderer.invoke('db:getInventory', params),
    updateInventory: (productId: number, quantity: number, type: string) => ipcRenderer.invoke('db:updateInventory', productId, quantity, type),
  },

  // Authentication operations
  auth: {
    login: (username: string, password: string) => ipcRenderer.invoke('auth:login', username, password),
    logout: (sessionId?: string) => ipcRenderer.invoke('auth:logout', sessionId),
    getCurrentUser: () => ipcRenderer.invoke('auth:getCurrentUser'),
    validateSession: (sessionId: string) => ipcRenderer.invoke('auth:validateSession', sessionId),
    getUserPermissions: (sessionId: string) => ipcRenderer.invoke('auth:getUserPermissions', sessionId),
  },
  
  // File operations
  files: {
    selectImage: () => ipcRenderer.invoke('files:selectImage'),
    saveImage: (imageData: any, filename: string) => ipcRenderer.invoke('files:saveImage', imageData, filename),
  },
  
  // Notifications
  notifications: {
    show: (title: string, body: string) => ipcRenderer.invoke('notifications:show', title, body),
  },

  // Debug operations
  debug: {
    checkUsers: () => ipcRenderer.invoke('debug:checkUsers'),
  },
  
  // Event listeners
  on: (channel: string, callback: Function) => {
    const validChannels = [
      'app:update-available',
      'app:update-downloaded',
      'database:error',
      'notification:clicked'
    ];
    
    if (validChannels.includes(channel)) {
      ipcRenderer.on(channel, (_event, ...args) => callback(...args));
    }
  },
  
  // Remove event listeners
  removeAllListeners: (channel: string) => {
    const validChannels = [
      'app:update-available',
      'app:update-downloaded',
      'database:error',
      'notification:clicked'
    ];
    
    if (validChannels.includes(channel)) {
      ipcRenderer.removeAllListeners(channel);
    }
  }
});

// Type definitions for the exposed API
declare global {
  interface Window {
    electronAPI: {
      getAppVersion: () => Promise<string>;
      minimizeWindow: () => Promise<void>;
      maximizeWindow: () => Promise<void>;
      closeWindow: () => Promise<void>;
      database: {
        getUsers: () => Promise<any[]>;
        createUser: (userData: any) => Promise<any>;
        getProducts: (params?: any) => Promise<any>;
        getProduct: (id: number) => Promise<any>;
        createProduct: (productData: any) => Promise<any>;
        updateProduct: (id: number, updates: any) => Promise<any>;
        deleteProduct: (id: number) => Promise<void>;
        getCategories: () => Promise<any[]>;
        getCustomers: (params?: any) => Promise<any>;
        createCustomer: (customerData: any) => Promise<any>;
        getOrders: (params?: any) => Promise<any>;
        createOrder: (orderData: any) => Promise<any>;
        getArtists: () => Promise<any[]>;
        getInventory: (params?: any) => Promise<any>;
        updateInventory: (productId: number, quantity: number, type: string) => Promise<any>;
      };
      auth: {
        login: (username: string, password: string) => Promise<{ user: any; sessionId: string; permissions: string[] }>;
        logout: (sessionId?: string) => Promise<void>;
        getCurrentUser: () => Promise<any>;
        validateSession: (sessionId: string) => Promise<{ isValid: boolean; user?: any; permissions?: string[] }>;
        getUserPermissions: (sessionId: string) => Promise<string[]>;
      };
      files: {
        selectImage: () => Promise<string>;
        saveImage: (imageData: any, filename: string) => Promise<string>;
      };
      notifications: {
        show: (title: string, body: string) => Promise<void>;
      };
      on: (channel: string, callback: Function) => void;
      removeAllListeners: (channel: string) => void;
    };
  }
}
