import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export type Theme = 'light' | 'dark' | 'auto';
export type Language = 'en' | 'hi' | 'mai'; // English, Hindi, Maithili

interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  timestamp: number;
}

interface UIState {
  theme: Theme;
  language: Language;
  sidebarCollapsed: boolean;
  loading: {
    global: boolean;
    products: boolean;
    customers: boolean;
    orders: boolean;
    inventory: boolean;
  };
  notifications: Notification[];
  modals: {
    productForm: boolean;
    customerForm: boolean;
    orderForm: boolean;
    confirmDialog: boolean;
  };
  currentPage: string;
  searchQuery: string;
  filters: {
    products: {
      category: string;
      priceRange: [number, number];
      inStock: boolean;
    };
    customers: {
      type: string;
      location: string;
    };
    orders: {
      status: string;
      dateRange: [string, string];
    };
  };
}

const initialState: UIState = {
  theme: 'auto',
  language: 'en',
  sidebarCollapsed: false,
  loading: {
    global: false,
    products: false,
    customers: false,
    orders: false,
    inventory: false,
  },
  notifications: [],
  modals: {
    productForm: false,
    customerForm: false,
    orderForm: false,
    confirmDialog: false,
  },
  currentPage: 'dashboard',
  searchQuery: '',
  filters: {
    products: {
      category: '',
      priceRange: [0, 10000],
      inStock: false,
    },
    customers: {
      type: '',
      location: '',
    },
    orders: {
      status: '',
      dateRange: ['', ''],
    },
  },
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    setTheme: (state, action: PayloadAction<Theme>) => {
      state.theme = action.payload;
    },
    setLanguage: (state, action: PayloadAction<Language>) => {
      state.language = action.payload;
    },
    toggleSidebar: (state) => {
      state.sidebarCollapsed = !state.sidebarCollapsed;
    },
    setSidebarCollapsed: (state, action: PayloadAction<boolean>) => {
      state.sidebarCollapsed = action.payload;
    },
    setLoading: (state, action: PayloadAction<{ key: keyof UIState['loading']; value: boolean }>) => {
      state.loading[action.payload.key] = action.payload.value;
    },
    addNotification: (state, action: PayloadAction<Omit<Notification, 'id' | 'timestamp'>>) => {
      const notification: Notification = {
        ...action.payload,
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        timestamp: Date.now(),
      };
      state.notifications.push(notification);
    },
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(n => n.id !== action.payload);
    },
    clearNotifications: (state) => {
      state.notifications = [];
    },
    openModal: (state, action: PayloadAction<keyof UIState['modals']>) => {
      state.modals[action.payload] = true;
    },
    closeModal: (state, action: PayloadAction<keyof UIState['modals']>) => {
      state.modals[action.payload] = false;
    },
    closeAllModals: (state) => {
      Object.keys(state.modals).forEach(key => {
        state.modals[key as keyof UIState['modals']] = false;
      });
    },
    setCurrentPage: (state, action: PayloadAction<string>) => {
      state.currentPage = action.payload;
    },
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },
    setProductFilters: (state, action: PayloadAction<Partial<UIState['filters']['products']>>) => {
      state.filters.products = { ...state.filters.products, ...action.payload };
    },
    setCustomerFilters: (state, action: PayloadAction<Partial<UIState['filters']['customers']>>) => {
      state.filters.customers = { ...state.filters.customers, ...action.payload };
    },
    setOrderFilters: (state, action: PayloadAction<Partial<UIState['filters']['orders']>>) => {
      state.filters.orders = { ...state.filters.orders, ...action.payload };
    },
    resetFilters: (state) => {
      state.filters = initialState.filters;
      state.searchQuery = '';
    },
  },
});

export const {
  setTheme,
  setLanguage,
  toggleSidebar,
  setSidebarCollapsed,
  setLoading,
  addNotification,
  removeNotification,
  clearNotifications,
  openModal,
  closeModal,
  closeAllModals,
  setCurrentPage,
  setSearchQuery,
  setProductFilters,
  setCustomerFilters,
  setOrderFilters,
  resetFilters,
} = uiSlice.actions;

export default uiSlice.reducer;
