import { ipcMain, app, BrowserWindow, dialog, Notification } from 'electron';
import * as path from 'path';
import * as fs from 'fs';
import { userService } from '../services/userService';
import { productService } from '../services/productService';
import { databaseService } from '../database/database';
import { sessionService } from '../services/sessionService';
import { rbacService } from '../services/rbacService';

/**
 * Register all IPC handlers for the main process
 */
export function registerIpcHandlers(): void {
  // App-related handlers
  ipcMain.handle('app:getVersion', () => {
    return app.getVersion();
  });

  // Window control handlers
  ipcMain.handle('window:minimize', () => {
    const window = BrowserWindow.getFocusedWindow();
    if (window) {
      window.minimize();
    }
  });

  ipcMain.handle('window:maximize', () => {
    const window = BrowserWindow.getFocusedWindow();
    if (window) {
      if (window.isMaximized()) {
        window.unmaximize();
      } else {
        window.maximize();
      }
    }
  });

  ipcMain.handle('window:close', () => {
    const window = BrowserWindow.getFocusedWindow();
    if (window) {
      window.close();
    }
  });

  // File operation handlers
  ipcMain.handle('files:selectImage', async () => {
    const result = await dialog.showOpenDialog({
      properties: ['openFile'],
      filters: [
        { name: 'Images', extensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'] }
      ]
    });

    if (!result.canceled && result.filePaths.length > 0) {
      return result.filePaths[0];
    }
    return null;
  });

  ipcMain.handle('files:saveImage', async (_event, imageData: Buffer, filename: string) => {
    try {
      const userDataPath = app.getPath('userData');
      const imagesDir = path.join(userDataPath, 'images');
      
      // Create images directory if it doesn't exist
      if (!fs.existsSync(imagesDir)) {
        fs.mkdirSync(imagesDir, { recursive: true });
      }

      const filePath = path.join(imagesDir, filename);
      fs.writeFileSync(filePath, imageData);
      
      return filePath;
    } catch (error) {
      console.error('Error saving image:', error);
      throw error;
    }
  });

  // Notification handlers
  ipcMain.handle('notifications:show', (_event, title: string, body: string) => {
    if (Notification.isSupported()) {
      const notification = new Notification({
        title,
        body,
        icon: path.join(__dirname, '../../../assets/icons/app-icon.png')
      });
      
      notification.show();
      
      notification.on('click', () => {
        const window = BrowserWindow.getFocusedWindow();
        if (window) {
          if (window.isMinimized()) {
            window.restore();
          }
          window.focus();
        }
      });
    }
  });

  // Database handlers - now using real database services
  ipcMain.handle('db:getUsers', async () => {
    try {
      const result = await userService.getUsers();
      if (result.success && result.data) {
        // Remove sensitive data before sending to renderer
        const safeUsers = result.data.data.map(user => {
          const { passwordHash, salt, ...safeUser } = user;
          return safeUser;
        });
        return { ...result.data, data: safeUsers };
      }
      return { data: [], total: 0, page: 1, limit: 50, totalPages: 0 };
    } catch (error) {
      console.error('Error getting users:', error);
      return { data: [], total: 0, page: 1, limit: 50, totalPages: 0 };
    }
  });

  ipcMain.handle('db:createUser', async (_event, userData: any) => {
    try {
      const result = await userService.createUser(userData);
      if (result.success && result.data) {
        const { passwordHash, salt, ...safeUser } = result.data;
        return safeUser;
      }
      throw new Error(result.error || 'Failed to create user');
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  });

  ipcMain.handle('db:getProducts', async (_event, params?: any) => {
    try {
      const result = await productService.getProducts(params);
      if (result.success && result.data) {
        return result.data;
      }
      return { data: [], total: 0, page: 1, limit: 50, totalPages: 0 };
    } catch (error) {
      console.error('Error getting products:', error);
      return { data: [], total: 0, page: 1, limit: 50, totalPages: 0 };
    }
  });

  ipcMain.handle('db:getProduct', async (_event, id: number) => {
    try {
      const result = await productService.getProduct(id);
      if (result.success && result.data) {
        return result.data;
      }
      throw new Error(result.error || 'Product not found');
    } catch (error) {
      console.error('Error getting product:', error);
      throw error;
    }
  });

  ipcMain.handle('db:createProduct', async (_event, productData: any) => {
    try {
      const result = await productService.createProduct(productData);
      if (result.success && result.data) {
        return result.data;
      }
      throw new Error(result.error || 'Failed to create product');
    } catch (error) {
      console.error('Error creating product:', error);
      throw error;
    }
  });

  ipcMain.handle('db:updateProduct', async (_event, id: number, updates: any) => {
    try {
      const result = await productService.updateProduct(id, updates);
      if (result.success && result.data) {
        return result.data;
      }
      throw new Error(result.error || 'Failed to update product');
    } catch (error) {
      console.error('Error updating product:', error);
      throw error;
    }
  });

  ipcMain.handle('db:deleteProduct', async (_event, id: number) => {
    try {
      const result = await productService.deleteProduct(id);
      if (!result.success) {
        throw new Error(result.error || 'Failed to delete product');
      }
    } catch (error) {
      console.error('Error deleting product:', error);
      throw error;
    }
  });

  ipcMain.handle('db:getCustomers', async (_event, params?: any) => {
    try {
      const { page = 1, limit = 50, search = '' } = params || {};
      let sql = 'SELECT * FROM customers WHERE is_active = 1';
      const queryParams: any[] = [];

      if (search) {
        sql += ' AND (name LIKE ? OR phone LIKE ? OR email LIKE ?)';
        const searchTerm = `%${search}%`;
        queryParams.push(searchTerm, searchTerm, searchTerm);
      }

      sql += ' ORDER BY name';

      const result = await databaseService.paginate(sql, queryParams, page, limit);
      if (result.success && result.data) {
        return result.data;
      }
      return { data: [], total: 0, page: 1, limit: 50, totalPages: 0 };
    } catch (error) {
      console.error('Error getting customers:', error);
      return { data: [], total: 0, page: 1, limit: 50, totalPages: 0 };
    }
  });

  ipcMain.handle('db:createCustomer', async (_event, customerData: any) => {
    try {
      const result = await databaseService.execute(`
        INSERT INTO customers (
          customer_code, name, name_hindi, email, phone, address, city, state, pincode,
          country, customer_type, is_active
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
      `, [
        customerData.customerCode || `CUST${Date.now()}`,
        customerData.name,
        customerData.nameHindi || null,
        customerData.email || null,
        customerData.phone,
        customerData.address,
        customerData.city,
        customerData.state,
        customerData.pincode,
        customerData.country || 'India',
        customerData.customerType || 'individual'
      ]);

      if (result.success && result.data) {
        return { id: result.data.lastInsertRowid, ...customerData };
      }
      throw new Error(result.error || 'Failed to create customer');
    } catch (error) {
      console.error('Error creating customer:', error);
      throw error;
    }
  });

  ipcMain.handle('db:getOrders', async (_event, params?: any) => {
    try {
      const { page = 1, limit = 50, status = '' } = params || {};
      let sql = `
        SELECT
          o.*,
          c.name as customerName,
          u.full_name as createdBy
        FROM orders o
        LEFT JOIN customers c ON o.customer_id = c.id
        LEFT JOIN users u ON o.user_id = u.id
      `;
      const queryParams: any[] = [];

      if (status) {
        sql += ' WHERE o.status = ?';
        queryParams.push(status);
      }

      sql += ' ORDER BY o.created_at DESC';

      const result = await databaseService.paginate(sql, queryParams, page, limit);
      if (result.success && result.data) {
        return result.data;
      }
      return { data: [], total: 0, page: 1, limit: 50, totalPages: 0 };
    } catch (error) {
      console.error('Error getting orders:', error);
      return { data: [], total: 0, page: 1, limit: 50, totalPages: 0 };
    }
  });

  ipcMain.handle('db:createOrder', async (_event, orderData: any) => {
    try {
      const orderNumber = `ORD${Date.now()}`;
      const result = await databaseService.execute(`
        INSERT INTO orders (
          order_number, customer_id, subtotal, tax_amount, discount_amount,
          shipping_amount, total_amount, shipping_address, billing_address,
          user_id, status, payment_status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'draft', 'pending')
      `, [
        orderNumber,
        orderData.customerId,
        orderData.subtotal || 0,
        orderData.taxAmount || 0,
        orderData.discountAmount || 0,
        orderData.shippingAmount || 0,
        orderData.totalAmount || 0,
        orderData.shippingAddress,
        orderData.billingAddress,
        orderData.userId || 1
      ]);

      if (result.success && result.data) {
        return { id: result.data.lastInsertRowid, orderNumber, ...orderData };
      }
      throw new Error(result.error || 'Failed to create order');
    } catch (error) {
      console.error('Error creating order:', error);
      throw error;
    }
  });

  // Additional database handlers
  ipcMain.handle('db:getStats', async () => {
    try {
      const result = await databaseService.getStats();
      return result.data || {};
    } catch (error) {
      console.error('Error getting database stats:', error);
      return {};
    }
  });

  ipcMain.handle('db:getCategories', async () => {
    try {
      const result = await databaseService.query('SELECT * FROM categories WHERE is_active = 1 ORDER BY sort_order, name');
      return result.data || [];
    } catch (error) {
      console.error('Error getting categories:', error);
      return [];
    }
  });

  ipcMain.handle('db:getArtists', async () => {
    try {
      const result = await databaseService.query('SELECT * FROM artists WHERE is_active = 1 ORDER BY name');
      return result.data || [];
    } catch (error) {
      console.error('Error getting artists:', error);
      return [];
    }
  });

  ipcMain.handle('db:getInventory', async (_event, params?: any) => {
    try {
      const { page = 1, limit = 50 } = params || {};
      const sql = `
        SELECT
          i.*,
          p.name as productName,
          p.sku as productSku
        FROM inventory i
        LEFT JOIN products p ON i.product_id = p.id
        ORDER BY p.name
      `;

      const result = await databaseService.paginate(sql, [], page, limit);
      if (result.success && result.data) {
        return result.data;
      }
      return { data: [], total: 0, page: 1, limit: 50, totalPages: 0 };
    } catch (error) {
      console.error('Error getting inventory:', error);
      return { data: [], total: 0, page: 1, limit: 50, totalPages: 0 };
    }
  });

  ipcMain.handle('db:updateInventory', async (_event, productId: number, quantity: number, type: string) => {
    try {
      const adjustment = type === 'add' ? quantity : -quantity;
      const result = await databaseService.execute(`
        UPDATE inventory
        SET
          current_stock = current_stock + ?,
          available_stock = current_stock - reserved_stock,
          last_stock_update = datetime('now'),
          updated_at = datetime('now')
        WHERE product_id = ?
      `, [adjustment, productId]);

      if (result.success) {
        // Get updated inventory record
        const updated = await databaseService.queryOne(`
          SELECT
            i.*,
            p.name as productName,
            p.sku as productSku
          FROM inventory i
          LEFT JOIN products p ON i.product_id = p.id
          WHERE i.product_id = ?
        `, [productId]);

        return updated.data;
      }
      throw new Error(result.error || 'Failed to update inventory');
    } catch (error) {
      console.error('Error updating inventory:', error);
      throw error;
    }
  });

  // Authentication handlers
  ipcMain.handle('auth:login', async (_event, username: string, password: string) => {
    try {
      const result = await userService.authenticateUser(username, password);

      if (result.success && result.data) {
        const { passwordHash, salt, ...safeUser } = result.data;

        // Create session
        const sessionId = sessionService.createSession(safeUser);

        // Get user permissions
        const permissions = rbacService.getUserPermissions(safeUser);

        return {
          user: safeUser,
          sessionId,
          permissions,
        };
      }
      throw new Error(result.error || 'Authentication failed');
    } catch (error) {
      console.error('Error during login:', error);
      throw error;
    }
  });

  ipcMain.handle('auth:logout', async (_event, sessionId?: string) => {
    try {
      if (sessionId) {
        const destroyed = sessionService.destroySession(sessionId);
        return { success: destroyed };
      }
      return { success: true };
    } catch (error) {
      console.error('Error during logout:', error);
      return { success: false, error: String(error) };
    }
  });

  ipcMain.handle('auth:validateSession', async (_event, sessionId: string) => {
    try {
      const validation = sessionService.validateSession(sessionId);
      if (validation.isValid && validation.user) {
        const permissions = rbacService.getUserPermissions(validation.user);
        return {
          isValid: true,
          user: validation.user,
          permissions,
        };
      }
      return { isValid: false };
    } catch (error) {
      console.error('Error validating session:', error);
      return { isValid: false };
    }
  });

  ipcMain.handle('auth:getUserPermissions', async (_event, sessionId: string) => {
    try {
      const user = sessionService.getCurrentUser(sessionId);
      if (user) {
        return rbacService.getUserPermissions(user);
      }
      return [];
    } catch (error) {
      console.error('Error getting user permissions:', error);
      return [];
    }
  });

  ipcMain.handle('auth:getCurrentUser', async () => {
    // For now, return a default user - in a real app you'd get from session
    try {
      const result = await databaseService.queryOne('SELECT * FROM users WHERE role = "admin" LIMIT 1');
      if (result.success && result.data) {
        const { passwordHash, salt, ...safeUser } = result.data;
        return safeUser;
      }
      return null;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  });

  console.log('✅ All IPC handlers registered successfully');
}
