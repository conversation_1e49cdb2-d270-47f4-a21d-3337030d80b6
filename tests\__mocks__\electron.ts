export const app = {
  getPath: jest.fn(() => '/mock/path'),
  whenReady: jest.fn(() => Promise.resolve()),
  quit: jest.fn(),
  on: jest.fn(),
};

export const BrowserWindow = jest.fn().mockImplementation(() => ({
  loadFile: jest.fn(),
  loadURL: jest.fn(),
  on: jest.fn(),
  webContents: {
    openDevTools: jest.fn(),
    on: jest.fn(),
  },
  show: jest.fn(),
  close: jest.fn(),
}));

export const Menu = {
  setApplicationMenu: jest.fn(),
  buildFromTemplate: jest.fn(),
};

export const ipcMain = {
  handle: jest.fn(),
  on: jest.fn(),
  removeAllListeners: jest.fn(),
};

export const ipcRenderer = {
  invoke: jest.fn(),
  send: jest.fn(),
  on: jest.fn(),
  removeAllListeners: jest.fn(),
};

export const contextBridge = {
  exposeInMainWorld: jest.fn(),
};

export default {
  app,
  BrowserWindow,
  Menu,
  ipcMain,
  ipcRenderer,
  contextBridge,
};
