import React from 'react';
import { createRoot } from 'react-dom/client';
import { Provider } from 'react-redux';
import { FluentProvider, webLightTheme, webDarkTheme } from '@fluentui/react-components';
import { store } from './store';
import App from './App';
import './styles/global.css';

// Get the root element
const container = document.getElementById('root');
if (!container) {
  throw new Error('Root element not found');
}

// Create root and render the app
const root = createRoot(container);

// Determine theme based on system preference
const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
const theme = prefersDark ? webDarkTheme : webLightTheme;

root.render(
  <React.StrictMode>
    <Provider store={store}>
      <FluentProvider theme={theme}>
        <App />
      </FluentProvider>
    </Provider>
  </React.StrictMode>
);
