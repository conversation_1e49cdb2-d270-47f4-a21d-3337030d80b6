import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Order } from '../../../shared/types/database';

interface OrdersState {
  selectedOrder: Order | null;
  recentOrders: Order[];
  sortBy: 'order_date' | 'total_amount' | 'status' | 'customer_name';
  sortOrder: 'asc' | 'desc';
  statusFilter: string;
  pageSize: number;
  currentPage: number;
}

const initialState: OrdersState = {
  selectedOrder: null,
  recentOrders: [],
  sortBy: 'order_date',
  sortOrder: 'desc',
  statusFilter: '',
  pageSize: 20,
  currentPage: 1,
};

const ordersSlice = createSlice({
  name: 'orders',
  initialState,
  reducers: {
    setSelectedOrder: (state, action: PayloadAction<Order | null>) => {
      state.selectedOrder = action.payload;
    },
    setSortBy: (state, action: PayloadAction<OrdersState['sortBy']>) => {
      state.sortBy = action.payload;
    },
    setSortOrder: (state, action: PayloadAction<OrdersState['sortOrder']>) => {
      state.sortOrder = action.payload;
    },
    setStatusFilter: (state, action: PayloadAction<string>) => {
      state.statusFilter = action.payload;
    },
    setPageSize: (state, action: PayloadAction<number>) => {
      state.pageSize = action.payload;
      state.currentPage = 1;
    },
    setCurrentPage: (state, action: PayloadAction<number>) => {
      state.currentPage = action.payload;
    },
  },
});

export const {
  setSelectedOrder,
  setSortBy,
  setSortOrder,
  setStatusFilter,
  setPageSize,
  setCurrentPage,
} = ordersSlice.actions;

export default ordersSlice.reducer;
